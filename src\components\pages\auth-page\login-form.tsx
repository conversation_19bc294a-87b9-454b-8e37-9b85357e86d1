"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { FormControl } from "@/components/form-control";
import PasswordInput from "@/components/password-input";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function LoginForm({ onForgot }: { onForgot: () => void }) {
  const t = useTranslations();
  const loginSchema = z.object({
    email: z.string().email({ message: t("Auth.emailError") }),
    password: z
      .string()
      .min(6, { message: t("PasswordInput.passwordChecklist.length") })
      .regex(/[A-Z]/, {
        message: t("PasswordInput.passwordChecklist.uppercase"),
      })
      .regex(/\d/, { message: t("PasswordInput.passwordChecklist.number") })
      .regex(/[!@#$%^&*(),.?":{}|<>]/, {
        message: t("PasswordInput.passwordChecklist.special"),
      }),
  });
  type LoginFormValues = z.infer<typeof loginSchema>;
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = (data: LoginFormValues) => {
    console.log("Login", data);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <FormControl
        label={t("Auth.email")}
        id="email"
        error={form.formState.errors.email?.message}
        isRequired
      >
        <Input type="email" {...form.register("email")} />
      </FormControl>

      <FormControl label={t("Auth.password")} id="login-password" isRequired>
        <PasswordInput {...form.register("password")} />
      </FormControl>

      <Button type="submit" className="w-full">
        {t("Auth.login")}
      </Button>

      <div className="text-right text-sm">
        <button
          type="button"
          onClick={onForgot}
          className="text-blue-600 hover:underline"
        >
          {t("Auth.forgotPassword")}
        </button>
      </div>
    </form>
  );
}
