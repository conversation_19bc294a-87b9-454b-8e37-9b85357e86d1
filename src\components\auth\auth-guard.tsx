'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthContext } from '@/components/providers/auth-provider';
import { AuthLoadingScreen } from '@/components/providers/auth-provider';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export function AuthGuard({
  children,
  requireAuth = true,
  redirectTo,
  fallback,
}: AuthGuardProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isInitialized, isLoading } = useAuthContext();

  useEffect(() => {
    if (!isInitialized || isLoading) {
      return;
    }

    if (requireAuth && !isAuthenticated) {
      // Redirect to login with return URL
      const currentPath = window.location.pathname;
      const loginUrl = redirectTo || '/login';
      const redirectUrl = `${loginUrl}?redirect=${encodeURIComponent(currentPath)}`;
      router.push(redirectUrl);
      return;
    }

    if (!requireAuth && isAuthenticated) {
      // Redirect authenticated users away from auth pages
      const redirectPath = searchParams.get('redirect') || '/dashboard';
      router.push(redirectPath);
      return;
    }
  }, [isAuthenticated, isInitialized, isLoading, requireAuth, router, redirectTo, searchParams]);

  // Show loading screen while initializing
  if (!isInitialized || isLoading) {
    return fallback || <AuthLoadingScreen />;
  }

  // Show fallback if auth requirements are not met
  if (requireAuth && !isAuthenticated) {
    return fallback || <AuthLoadingScreen />;
  }

  if (!requireAuth && isAuthenticated) {
    return fallback || <AuthLoadingScreen />;
  }

  return <>{children}</>;
}

// Specific guards for common use cases
export function ProtectedRoute({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <AuthGuard requireAuth={true} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

export function PublicRoute({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <AuthGuard requireAuth={false} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

// HOC versions
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, 'children'> = {}
) {
  return function GuardedComponent(props: P) {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>
) {
  return withAuthGuard(Component, { requireAuth: true });
}

export function withPublicRoute<P extends object>(
  Component: React.ComponentType<P>
) {
  return withAuthGuard(Component, { requireAuth: false });
}
