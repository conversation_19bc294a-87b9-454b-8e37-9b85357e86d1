import {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '@/lib/types/auth';

const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';
const REFRESH_TOKEN_ENDPOINT = `${API_BASE_URL}/Auth/refresh-token`; // Update this when endpoint is ready

class AuthApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'AuthApiError';
  }
}

async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorCode = response.status.toString();

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
      errorCode = errorData.code || errorCode;
    } catch {
      // If response is not JSON, use default error message
    }

    throw new AuthApiError(errorMessage, response.status, errorCode);
  }

  return response.json();
}

export const authApi = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiRequest<{
        data: {
          accessToken: string;
          accessTokenExpiration: string;
          refreshToken: string;
          refreshTokenExpiration: string;
        };
        errorMessages: string[] | null;
      }>(
        `${API_BASE_URL}/Auth/authenticate-user`,
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        }
      );

      console.log('API Response:', response); // Debug log

      // Check for API errors
      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new AuthApiError(response.errorMessages.join(', '));
      }

      if (!response.data) {
        throw new AuthApiError('Invalid response format from server');
      }

      // Parse JWT token to extract user info
      let userInfo: {
        id: string;
        email: string;
        name: string;
        role: string;
        permissions?: string[];
      } = {
        id: 'unknown',
        email: credentials.email,
        name: 'User',
        role: 'user',
      };

      try {
        const tokenPayload = JSON.parse(atob(response.data.accessToken.split('.')[1]));
        console.log('JWT Payload:', tokenPayload); // Debug log

        userInfo = {
          id: tokenPayload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'] || 'unknown',
          email: tokenPayload.email || credentials.email,
          name: tokenPayload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] || 'User',
          role: 'admin', // Based on the permissions, this looks like an admin user
          permissions: tokenPayload.permission || [],
        };
      } catch (jwtError) {
        console.warn('Could not parse JWT token:', jwtError);
      }

      // Calculate expiresIn from accessTokenExpiration
      const expirationDate = new Date(response.data.accessTokenExpiration);
      const currentDate = new Date();
      const expiresIn = Math.floor((expirationDate.getTime() - currentDate.getTime()) / 1000);

      // Transform the response to match our expected format
      const transformedResponse: LoginResponse = {
        user: userInfo,
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        expiresIn: expiresIn,
      };

      console.log('Transformed Response:', transformedResponse); // Debug log

      return transformedResponse;
    } catch (error) {
      console.error('Login API Error:', error); // Debug log
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during login');
    }
  },

  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    try {
      const response = await apiRequest<{
        data: {
          accessToken: string;
          accessTokenExpiration: string;
          refreshToken: string;
          refreshTokenExpiration: string;
        };
        errorMessages: string[] | null;
      }>(
        REFRESH_TOKEN_ENDPOINT,
        {
          method: 'POST',
          body: JSON.stringify(request),
        }
      );

      console.log('Refresh Token API Response:', response); // Debug log

      // Check for API errors
      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new AuthApiError(response.errorMessages.join(', '));
      }

      if (!response.data) {
        throw new AuthApiError('Invalid response format from server');
      }

      // Calculate expiresIn from accessTokenExpiration
      const expirationDate = new Date(response.data.accessTokenExpiration);
      const currentDate = new Date();
      const expiresIn = Math.floor((expirationDate.getTime() - currentDate.getTime()) / 1000);

      return {
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        expiresIn: expiresIn,
      };
    } catch (error) {
      console.error('Refresh Token API Error:', error); // Debug log
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during token refresh');
    }
  },

  async logout(token: string): Promise<void> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/logout`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      // Logout should not fail the client-side logout process
      console.warn('Server logout failed:', error);
    }
  },

  async validateToken(token: string): Promise<boolean> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/validate`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      return false;
    }
  },
};

export { AuthApiError };
