import { AgentDispatchClient } from "livekit-server-sdk";
import { NextResponse } from "next/server";

export const revalidate = 0;

export async function POST(request: Request) {
  const body = await request.json();
  const LIVEKIT_URL = "wss://livekit2.enmdigital.com";
  const LIVEKIT_API_KEY = "APIY4EKemt3TCs5";
  const LIVEKIT_API_SECRET = "ORPuUbYK9deNMh6UEIGtCjSvHHJ1sfaL32T9p4VJAFS";

  const dispatchClient = new AgentDispatchClient(
    LIVEKIT_URL,
    LIVEKIT_API_KEY,
    LIVEKIT_API_SECRET
  );
  const roomName = `call-${Date.now()}`;

  const agentName = "dynamic3-agent";
  const metadata = {
    ...body.metadata,
    phonenumber: body.phonenumber,
  };

  await dispatchClient.createDispatch(roomName, agentName, {
    metadata: JSON.stringify(metadata),
  });
  return NextResponse.json({ success: true });
}
