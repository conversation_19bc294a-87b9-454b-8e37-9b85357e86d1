"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";
import ForgotPasswordForm from "@/components/pages/auth-page/forgot-password-form";
import LoginForm from "@/components/pages/auth-page/login-form";
import RegisterForm from "@/components/pages/auth-page/register-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const AuthCard = () => {
  const [form, setForm] = useState<"login" | "register" | "forgot">("login");
  const t = useTranslations();

  return (
    <Card className="w-full max-w-md h-[30rem] max-h-full">
      <CardContent>
        {form !== "forgot" && (
          <div className="flex space-x-4 mb-8">
            <Button
              variant={form === "login" ? "default" : "outline"}
              className="flex-1"
              onClick={() => setForm("login")}
            >
              {t("Auth.login")}
            </Button>
            <Button
              variant={form === "register" ? "default" : "outline"}
              className="flex-1"
              onClick={() => setForm("register")}
            >
              {t("Auth.register")}
            </Button>
          </div>
        )}

        {form === "login" && <LoginForm onForgot={() => setForm("forgot")} />}
        {form === "register" && <RegisterForm />}
        {form === "forgot" && (
          <ForgotPasswordForm onBack={() => setForm("login")} />
        )}
      </CardContent>
    </Card>
  );
};

export default AuthCard;
