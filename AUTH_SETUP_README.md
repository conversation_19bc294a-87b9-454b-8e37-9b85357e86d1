# Voice AI - Authentication System

<PERSON><PERSON> do<PERSON>, Voice AI uygulaması için kurulan kapsamlı authentication sistemini açıklar.

## 🚀 <PERSON><PERSON>lum Tamamlandı

Aşağıdaki bileşenler başarıyla kuruldu:

### 1. **Auth Store ve Types** (`src/store/auth-store.ts`, `src/lib/types/auth.ts`)
- Zustand ile state management
- LocalStorage ile persistence
- TypeScript type definitions
- Auto token refresh

### 2. **API Services** (`src/lib/api/auth.ts`)
- Login endpoint: `https://voiceaiapi.enmdigital.com/api/Auth/authenticate-user`
- Refresh token endpoint: `https://placeholder-refresh-endpoint.com/api/Auth/refresh` (placeholder)
- Error handling ve type safety

### 3. **Auth Hooks** (`src/hooks/use-auth.ts`)
- Tanstack Query integration
- Auto token refresh
- Login/logout mutations
- Token validation

### 4. **Auth Provider** (`src/components/providers/auth-provider.tsx`)
- React Context API
- Auth state management
- HOC for protected components

### 5. **Login Page** (`src/app/login/page.tsx`)
- React Hook Form ile form management
- Zod validation
- Responsive design
- Error handling

### 6. **Auth Guards** (`src/components/auth/auth-guard.tsx`)
- Protected routes
- Public routes
- HOC patterns
- Automatic redirects

### 7. **Middleware** (`src/middleware.ts`)
- Route protection
- Token validation
- Automatic redirects
- Next-intl integration

## 📁 Dosya Yapısı

```
src/
├── app/
│   ├── login/page.tsx          # Login sayfası
│   ├── dashboard/page.tsx      # Protected dashboard
│   └── layout.tsx              # Root layout with providers
├── components/
│   ├── auth/
│   │   ├── auth-guard.tsx      # Route guards
│   │   └── logout-button.tsx   # Logout component
│   └── providers/
│       └── auth-provider.tsx   # Auth context provider
├── hooks/
│   └── use-auth.ts            # Auth hook
├── lib/
│   ├── api/
│   │   └── auth.ts            # API services
│   ├── types/
│   │   └── auth.ts            # TypeScript types
│   └── validations/
│       └── auth.ts            # Zod schemas
├── store/
│   └── auth-store.ts          # Zustand store
└── middleware.ts              # Route protection
```

## 🔧 Kullanım

### Login Sayfası
```typescript
// Otomatik olarak /login route'unda mevcut
// Form validation ve error handling dahil
```

### Protected Route Oluşturma
```typescript
import { ProtectedRoute } from '@/components/auth/auth-guard';

export default function MyProtectedPage() {
  return (
    <ProtectedRoute>
      <div>Bu sayfa sadece giriş yapmış kullanıcılar görebilir</div>
    </ProtectedRoute>
  );
}
```

### Auth Hook Kullanımı
```typescript
import { useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const { user, isAuthenticated, login, logout, isLoading } = useAuth();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {isAuthenticated ? (
        <div>Welcome, {user?.email}!</div>
      ) : (
        <div>Please log in</div>
      )}
    </div>
  );
}
```

### Logout Button
```typescript
import { LogoutButton } from '@/components/auth/logout-button';

function Header() {
  return (
    <div>
      <LogoutButton showConfirmDialog={true} />
    </div>
  );
}
```

## 🛡️ Güvenlik Özellikleri

1. **Token Management**
   - Access token ve refresh token
   - Automatic token refresh
   - Secure storage (localStorage)

2. **Route Protection**
   - Middleware level protection
   - Component level guards
   - Automatic redirects

3. **Error Handling**
   - API error handling
   - Network error recovery
   - User-friendly error messages

4. **Type Safety**
   - Full TypeScript support
   - Zod validation
   - Type-safe API calls

## 🔄 Token Refresh

Token refresh otomatik olarak çalışır:
- Token süresi dolmadan 5 dakika önce refresh
- Background'da otomatik refresh
- Refresh başarısız olursa otomatik logout

## 📝 Yapılandırma

### API Endpoints
```typescript
// src/lib/api/auth.ts
const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';
const REFRESH_TOKEN_ENDPOINT = 'https://placeholder-refresh-endpoint.com/api/Auth/refresh'; // Güncellenmeli
```

### Protected Routes
```typescript
// src/middleware.ts
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/agents',
  '/analytics',
];
```

## 🚨 Önemli Notlar

1. **Refresh Token Endpoint**: Placeholder endpoint kullanılıyor, gerçek endpoint ile değiştirilmeli
2. **CORS**: API'da CORS ayarları yapılmalı
3. **Environment Variables**: API URL'leri environment variable'lara taşınabilir

## 🧪 Test Etme

1. `/{locale}/login` sayfasına git (örn: `/en/login`)
2. Email ve password gir
3. Başarılı login sonrası `/{locale}/dashboard`'a yönlendirileceksin
4. Protected route'ları test et
5. Logout işlemini test et

### Debug Test Sayfası
`/{locale}/test-login` sayfasında auth sistemini debug edebilirsiniz:
- Direct API test
- Store login test
- LocalStorage durumu
- Auth state görüntüleme

## 🐛 Sorun Giderme

### LocalStorage Boş Kalıyorsa:
1. Browser console'da debug log'ları kontrol edin
2. API response'unu kontrol edin
3. Network tab'da request/response'ları inceleyin
4. Test sayfasını kullanarak adım adım test edin

### Login Sonrası Yönlendirme Çalışmıyorsa:
1. Locale yapısını kontrol edin
2. Middleware'in doğru çalıştığını kontrol edin
3. Console'da error log'ları kontrol edin

## 🔧 Geliştirme

Yeni protected route eklemek için:
1. `middleware.ts`'de `protectedRoutes` array'ine ekle
2. Sayfada `<ProtectedRoute>` wrapper kullan

Yeni auth API endpoint'i eklemek için:
1. `src/lib/types/auth.ts`'de type'ları tanımla
2. `src/lib/api/auth.ts`'de API fonksiyonunu ekle
3. `src/hooks/use-auth.ts`'de hook'u genişlet
