import { cloneElement, type ReactElement } from "react";

interface FormControlProps {
  label: string;
  id: string;
  error?: string | null;
  children: ReactElement<React.InputHTMLAttributes<HTMLInputElement>>;
  isRequired?: boolean;
  isDisabled?: boolean;
}

export function FormControl({
  label,
  id,
  error,
  children,
  isRequired = false,
  isDisabled = false,
}: FormControlProps) {
  const invalid = Boolean(error);

  return (
    <div className="mb-4">
      <label
        htmlFor={id}
        className={`block mb-1 font-medium ${isDisabled ? "opacity-50" : ""}`}
      >
        {label}
        {isRequired && <span className="text-red-500 ml-1">*</span>}
      </label>

      {cloneElement(children, {
        id,
        disabled: isDisabled,
        "aria-invalid": invalid,
        "aria-describedby": invalid ? `${id}-error` : undefined,
      })}

      {invalid && (
        <p
          id={`${id}-error`}
          className="text-red-500 text-sm mt-1"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  );
}
