"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { FormControl } from "@/components/form-control";
import PasswordInput from "@/components/password-input";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function RegisterForm() {
  const t = useTranslations();

  const registerSchema = z.object({
    name: z.string().min(2, { message: t("Auth.nameError") }),
    email: z.string().email({ message: t("Auth.emailError") }),
    password: z
      .string()
      .min(6, { message: t("PasswordInput.passwordChecklist.length") })
      .regex(/[A-Z]/, {
        message: t("PasswordInput.passwordChecklist.uppercase"),
      })
      .regex(/\d/, { message: t("PasswordInput.passwordChecklist.number") })
      .regex(/[!@#$%^&*(),.?":{}|<>]/, {
        message: t("PasswordInput.passwordChecklist.special"),
      }),
  });

  type RegisterFormValues = z.infer<typeof registerSchema>;

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = (data: RegisterFormValues) => {
    console.log("Register", data);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <FormControl
        label={t("Auth.name")}
        id="name"
        error={form.formState.errors.name?.message}
        isRequired
      >
        <Input {...form.register("name")} />
      </FormControl>

      <FormControl
        label={t("Auth.email")}
        id="email"
        error={form.formState.errors.email?.message}
        isRequired
      >
        <Input type="email" {...form.register("email")} />
      </FormControl>

      <FormControl label={t("Auth.password")} id="register-password" isRequired>
        <PasswordInput {...form.register("password")} />
      </FormControl>

      <Button type="submit" className="w-full">
        {t("Auth.register")}
      </Button>
    </form>
  );
}
