"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { FormControl } from "@/components/form-control";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function ForgotPasswordForm({ onBack }: { onBack: () => void }) {
  const t = useTranslations();
  const forgotSchema = z.object({
    email: z.string().email({ message: t("Auth.emailError") }),
  });
  type ForgotPasswordFormValues = z.infer<typeof forgotSchema>;
  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotSchema),
  });

  const onSubmit = (data: ForgotPasswordFormValues) => {
    console.log("Forgot password", data);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold mb-4">{t("Auth.resetTitle")}</h2>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormControl
          label={t("Auth.email")}
          id="email"
          error={form.formState.errors.email?.message}
          isRequired
        >
          <Input type="email" {...form.register("email")} />
        </FormControl>

        <Button type="submit" className="w-full">
          {t("Auth.resetButton")}
        </Button>
      </form>
      <Button variant="link" className="mt-4 text-sm" onClick={onBack}>
        {t("Auth.backToLogin")}
      </Button>
    </div>
  );
}
