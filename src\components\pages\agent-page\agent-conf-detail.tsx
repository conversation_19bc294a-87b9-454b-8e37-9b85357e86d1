import AgentConfTooltip from "./agent-conf-tooltip";
import type { PropsWithChildren } from "react";

type AgentConfDetailProps = PropsWithChildren<{
  title?: string;
  description?: string;
  slowTooltipText?: string;
}>;

const AgentConfDetail = ({
  children,
  description,
  slowTooltipText,
  title,
}: AgentConfDetailProps) => {
  return (
    <div>
      <div className="mb-1">
        <div className="flex items-center gap-1">
          {title && <p className="text-sm font-semibold">{title}</p>}
          {slowTooltipText && <AgentConfTooltip text={slowTooltipText} />}
        </div>
        {description && <p className="text-xs opacity-60">{description}</p>}
      </div>
      {children}
    </div>
  );
};

export default AgentConfDetail;
