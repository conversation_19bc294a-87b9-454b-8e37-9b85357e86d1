"use client";

import {
  Book,
  Calendar,
  Download,
  Edit,
  FileText,
  Folder,
  Plus,
  Trash2,
} from "lucide-react";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface KnowledgeBaseItem {
  id: string;
  name: string;
  filesCount: number;
  uploadDate: string;
}

interface FileItem {
  id: string;
  name: string;
  size: string;
  type: "txt" | "pdf" | "doc";
}

const knowledgeBaseData: KnowledgeBaseItem[] = [
  {
    id: "1",
    name: "Pegasus Short",
    filesCount: 9,
    uploadDate: "06/30/2025 17:53",
  },
  { id: "2", name: "z", filesCount: 0, uploadDate: "06/30/2025 15:30" },
  { id: "3", name: "Pegasus", filesCount: 5, uploadDate: "06/29/2025 14:20" },
  {
    id: "4",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    filesCount: 12,
    uploadDate: "06/29/2025 10:15",
  },
  {
    id: "5",
    name: "CeyBer Digital",
    filesCount: 8,
    uploadDate: "06/28/2025 16:45",
  },
  {
    id: "6",
    name: "SunExpress (Bagaj) Knowledge...",
    filesCount: 15,
    uploadDate: "06/28/2025 12:30",
  },
  {
    id: "7",
    name: "SunExpress SSS",
    filesCount: 20,
    uploadDate: "06/27/2025 09:20",
  },
  {
    id: "8",
    name: "İhlas Pazarlama Anket Knowle...",
    filesCount: 7,
    uploadDate: "06/26/2025 14:10",
  },
  {
    id: "9",
    name: "Telkotürk Sıkça Sorulan Sorular",
    filesCount: 25,
    uploadDate: "06/25/2025 11:55",
  },
  {
    id: "10",
    name: "Sahibinden İlan Bilgi",
    filesCount: 18,
    uploadDate: "06/24/2025 13:40",
  },
];

const fileData: FileItem[] = [
  { id: "1", name: "1.txt", size: "0 K", type: "txt" },
  { id: "2", name: "2.txt", size: "5 K", type: "txt" },
  { id: "3", name: "3.txt", size: "1 K", type: "txt" },
  { id: "4", name: "4.txt", size: "0 K", type: "txt" },
  { id: "5", name: "5.txt", size: "3 K", type: "txt" },
  { id: "6", name: "6.txt", size: "2 K", type: "txt" },
  { id: "7", name: "7.txt", size: "11 K", type: "txt" },
  { id: "8", name: "8.txt", size: "9 K", type: "txt" },
  { id: "9", name: "9.txt", size: "5 K", type: "txt" },
];

export default function KnowledgeBasePage() {
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] =
    useState<KnowledgeBaseItem>(knowledgeBaseData[0]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case "txt":
        return <FileText className="h-4 w-4 text-orange-500" />;
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "doc":
        return <FileText className="h-4 w-4 text-blue-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case "txt":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "pdf":
        return "bg-red-100 text-red-800 border-red-200";
      case "doc":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="flex-1 h-full p-4 md:px-8">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 h-full">
        <div className="lg:col-span-4 xl:col-span-3">
          <Card className="h-full">
            <CardHeader className="pb-2 flex justify-between border-b h-12">
              <CardTitle className="text-sm flex items-center gap-1">
                <Book className="h-4 w-4" />
                Knowledge Base
              </CardTitle>
              <Button className="h-8 w-8">
                <Plus className="h-3 w-3 " />
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1 max-h-[calc(100vh-120px)] overflow-y-auto">
                {knowledgeBaseData.map((item) => (
                  <div
                    key={item.id}
                    className={`flex items-center space-x-3 p-2 cursor-pointer hover:bg-gray-50 border-l-4 transition-colors ${
                      selectedKnowledgeBase.id === item.id
                        ? "bg-blue-50 border-l-blue-500"
                        : "border-l-transparent"
                    }`}
                    onClick={() => setSelectedKnowledgeBase(item)}
                  >
                    <Folder className="h-4 w-4 text-gray-600 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p
                        className={`text-sm font-medium truncate ${
                          selectedKnowledgeBase.id === item.id
                            ? "text-blue-700"
                            : "text-gray-900"
                        }`}
                      >
                        {item.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-8 xl:col-span-9">
          <Card className="h-full">
            <CardHeader className="pb-3 border-b h-16">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">
                    {selectedKnowledgeBase.name}
                  </CardTitle>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>ID: know...ac6</span>
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Uploaded by: {selectedKnowledgeBase.uploadDate}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-black hover:bg-black cursor-pointer"
                  >
                    <Edit className="h-4 w-4 text-white" />
                    <p className="text-white">Edit</p>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="cursor-pointer"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
                  {fileData.map((file) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 transition-colors "
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {getFileIcon(file.type)}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">{file.size}</p>
                        </div>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getFileTypeColor(file.type)}`}
                        >
                          {file.type.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
