"use client";

import { Mic } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { PhoneInput } from "@/components/ui/phone-input";
import { useAgentConfigStore } from "@/store/agent-config";


const AgentTest = () => {
  const [phonenumber, setPhonenumber] = useState<string>("");
  const { config } = useAgentConfigStore();

  return (
    <div className="h-full flex items-center justify-center">
      <div className="flex gap-2 flex-col items-center">
        <Mic size={64} />
        <PhoneInput
          onChange={(value) => {
            setPhonenumber(value);
          }}
          defaultCountry="TR"
          countries={["TR"]}
        />
        <Button
          onClick={() => {
            fetch("/api/config", {
              method: "POST",
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                metadata: config,
                phonenumber: phonenumber.replace("+", "")
              }),
            });
          }}
          className="w-full"
        >
          Test your agent
        </Button>
      </div>
    </div>
  );
};

export default AgentTest;
