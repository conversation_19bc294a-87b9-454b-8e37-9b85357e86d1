"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { useState } from "react";
import { Bar } from "react-chartjs-2";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AddCustomChartModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const graphTypes = [
  { id: "column", name: "<PERSON>umn", icon: BarChart3 },
  { id: "bar", name: "<PERSON>", icon: BarChart2 },
  { id: "donut", name: "Donut", icon: <PERSON><PERSON><PERSON> },
  { id: "line", name: "<PERSON>", icon: <PERSON><PERSON><PERSON><PERSON><PERSON> },
  { id: "scatter", name: "<PERSON><PERSON><PERSON>", icon: <PERSON><PERSON><PERSON><PERSON><PERSON> },
];

const dateRanges = [
  { id: "last-4-weeks", name: "Last 4 weeks" },
  { id: "today", name: "Today" },
  { id: "last-week", name: "Last 1 week" },
  { id: "last-3-months", name: "Last 3 months" },
  { id: "week-to-date", name: "Week to date" },
  { id: "month-to-date", name: "Month to date" },
  { id: "year-to-date", name: "Year to date" },
  { id: "all-time", name: "All time" },
];

const agents = [
  { id: "all-agents", name: "All agents" },
  { id: "single-prompt-test", name: "Single-Prompt Agent Test (UI)" },
  { id: "pegasus-single", name: "Pegasus Single Prompt" },
  { id: "pegasus", name: "Pegasus" },
  { id: "yapikredi-senaryo", name: "Yapıkredi Senaryo 2 (Esnek)" },
];

const metrics = [
  { id: "call-counts", name: "Call Counts" },
  { id: "call-duration", name: "Call Duration" },
  { id: "call-latency", name: "Call Latency" },
  { id: "success-rate", name: "Success Rate" },
  { id: "pickup-rate", name: "Pickup Rate" },
  { id: "transfer-rate", name: "Transfer Rate" },
];

const viewByOptions = [
  { id: "hour", name: "Hour" },
  { id: "day", name: "Day" },
  { id: "week", name: "Week" },
  { id: "month", name: "Month" },
];

export default function AddCustomChartModal({
  isOpen,
  onClose,
}: AddCustomChartModalProps) {
  const [selectedGraphType, setSelectedGraphType] = useState("column");
  const [selectedDateRange, setSelectedDateRange] = useState("last-4-weeks");
  const [compareToPrevious, setCompareToPrevious] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState("all-agents");
  const [selectedMetrics, setSelectedMetrics] = useState(["call-counts"]);
  const [selectedViewBy, setSelectedViewBy] = useState("day");
  const [selectedSize, setSelectedSize] = useState("medium");

  const previewData = {
    labels: [
      "Jun 16, 2025",
      "Jun 18, 2025",
      "Jun 20, 2025",
      "Jun 23, 2025",
      "Jun 25, 2025",
      "Jun 27, 2025",
      "Jun 30, 2025",
      "Jul 02, 2025",
      "Jul 04, 2025",
      "Jul 07, 2025",
      "Jul 08, 2025",
      "Jul 10, 2025",
    ],
    datasets: [
      {
        label: "Call counts",
        data: [20, 15, 25, 18, 30, 22, 35, 28, 40, 45, 32, 38],
        backgroundColor: "rgb(59, 130, 246)",
        borderRadius: 4,
      },
    ],
  };

  const previewOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: "bottom" as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          color: "rgb(59, 130, 246)",
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 10,
          },
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
    },
  };

  const handleMetricAdd = () => {
    // Logic to add new metric
    console.log("Add metric clicked");
  };

  const handleSave = () => {
    const chartConfig = {
      graphType: selectedGraphType,
      dateRange: selectedDateRange,
      compareToPrevious,
      agent: selectedAgent,
      metrics: selectedMetrics,
      viewBy: selectedViewBy,
      size: selectedSize,
    };
    console.log("Chart configuration:", chartConfig);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="!max-w-[90rem] !w-[90vw] min-w-[1000px] max-h-[95vh] h-[95vh] overflow-y-auto"
        style={{ width: "90vw", maxWidth: "1200px", minWidth: "1000px" }}
      >
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            Add a Custom Chart
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Configuration */}
          <div className="space-y-6 col-span-1">
            {/* Graph Type */}
            <div>
              <h3 className="text-sm font-medium mb-3">Graph Type</h3>
              <div className="grid grid-cols-5 gap-2">
                {graphTypes.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <button
                      key={type.id}
                      onClick={() => setSelectedGraphType(type.id)}
                      className={`p-3 border rounded-lg flex flex-col items-center gap-1 hover:bg-gray-50 transition-colors ${
                        selectedGraphType === type.id
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200"
                      }`}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span className="text-xs">{type.name}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Date Range */}
            <div>
              <h3 className="text-sm font-medium mb-3">Date Range</h3>
              <Select
                value={selectedDateRange}
                onValueChange={setSelectedDateRange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dateRanges.map((range) => (
                    <SelectItem key={range.id} value={range.id}>
                      {range.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="mt-3 flex items-center space-x-2">
                <Checkbox
                  id="compare-previous"
                  checked={compareToPrevious}
                  onCheckedChange={(checked) =>
                    setCompareToPrevious(checked === true)
                  }
                />
                <label
                  htmlFor="compare-previous"
                  className="text-sm text-muted-foreground"
                >
                  Compare to previous period
                </label>
              </div>
              {selectedDateRange === "last-4-weeks" && (
                <div className="mt-2 p-3 bg-blue-50 rounded-md flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span className="text-xs text-blue-700">
                    The date range filter will override this charts date range.
                  </span>
                </div>
              )}
            </div>

            {/* Metrics */}
            <div>
              <h3 className="text-sm font-medium mb-3">Metrics</h3>
              <div className="space-y-3">
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedMetrics[0]}
                  onValueChange={(value) => setSelectedMetrics([value])}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {metrics.map((metric) => (
                      <SelectItem key={metric.id} value={metric.id}>
                        {metric.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMetricAdd}
                  className="w-fit"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>

            {/* View By */}
            <div>
              <h3 className="text-sm font-medium mb-3">View By</h3>
              <Tabs
                value={selectedViewBy}
                onValueChange={setSelectedViewBy}
                className="w-full"
              >
                <TabsList className="grid grid-cols-4 w-full">
                  {viewByOptions.map((option) => (
                    <TabsTrigger
                      key={option.id}
                      value={option.id}
                      className="text-sm"
                    >
                      {option.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="bg-gray-50 rounded-lg p-6 space-y-6  w-[55rem] flex flex-col items-center justify-around">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-700 mb-2">Chart</h3>
             
            </div>

            <div
              className={`bg-white rounded-lg p-4 shadow-sm 
            ${
              selectedSize === "small"
                ? "w-[25rem]"
                : selectedSize === "medium"
                ? "w-[35rem]"
                : "w-[50rem]"
            }`}
            >
              <div className="flex items-center gap-2 mb-4 ">
                <BarChart3 className="h-4 w-4 text-blue-600 " />
                <h4 className="text-sm font-medium">
                  {metrics.find((m) => m.id === selectedMetrics[0])?.name ||
                    "Call Counts"}
                </h4>
                <button className="ml-auto text-gray-400 hover:text-gray-600">
                  <span className="text-sm">✏️</span>
                </button>
              </div>
              <div className="text-xs text-muted-foreground mb-4">
                {agents.find((a) => a.id === selectedAgent)?.name ||
                  "All agents"}
              </div>
               <div className={`${
                selectedSize === "small" ? "h-56" : 
                selectedSize === "medium" ? "h-64" : 
                "h-72"
              }`}>
                <Bar data={previewData} options={previewOptions} />
              </div>
            </div>

            {/* Size Options */}
            <div className="flex justify-center">
              <Tabs
                value={selectedSize}
                onValueChange={setSelectedSize}
                className="w-fit"
              >
                <TabsList className="grid grid-cols-3">
                  <TabsTrigger value="small" className="text-sm">
                    Small
                  </TabsTrigger>
                  <TabsTrigger value="medium" className="text-sm">
                    Medium
                  </TabsTrigger>
                  <TabsTrigger value="large" className="text-sm">
                    Large
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-black text-white hover:bg-gray-800"
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
