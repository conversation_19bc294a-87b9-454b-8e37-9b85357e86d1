import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n";
import { NextRequest, NextResponse } from "next/server";

const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/agents',
  '/analytics',
];

const publicRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/',
];

// they're our redirect paths, if user is already authenticated
const authRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
];

function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route =>
    pathname.startsWith(route) || pathname.includes(route)
  );
}

function isAuthRoute(pathname: string): boolean {
  return authRoutes.some(route =>
    pathname.startsWith(route) || pathname.includes(route)
  );
}

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route =>
    pathname === route || pathname.startsWith(route)
  );
}

function getTokenFromRequest(request: NextRequest): string | null {
  const tokenFromCookie = request.cookies.get('voice-ai-auth')?.value;

  if (tokenFromCookie) {
    try {
      const authData = JSON.parse(tokenFromCookie);
      return authData?.state?.tokens?.accessToken || null;
    } catch {
      return null;
    }
  }

  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true; 
  }
}

async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = getTokenFromRequest(request);
  const isAuthenticated = token && !isTokenExpired(token);

  if (isAuthenticated && isAuthRoute(pathname)) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // If user is not authenticated and trying to access protected routes, redirect to login
  if (!isAuthenticated && isProtectedRoute(pathname)) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Allow the request to continue
  return NextResponse.next();
}

const intlMiddleware = createMiddleware(routing);

export default async function middleware(request: NextRequest) {
  // First run auth middleware
  const authResponse = await authMiddleware(request);

  // If auth middleware returns a redirect, return it
  if (authResponse.status === 302) {
    return authResponse;
  }

  // Otherwise, run the intl middleware
  return intlMiddleware(request);
}

export const config = {
  matcher: "/((?!api|trpc|_next|_vercel|.*\\..*).*)",
};
