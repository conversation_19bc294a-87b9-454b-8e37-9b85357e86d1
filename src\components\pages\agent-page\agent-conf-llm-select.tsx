"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAgentConfigStore } from "@/store/agent-config";

const llm_models = [
  { label: "GPT 4.1", value: { provider: "openai", model: "gpt-4.1" } },
  {
    label: "GPT 4.1 mini",
    value: { provider: "openai", model: "gpt-4.1-mini" },
  },
  {
    label: "GPT 4.1 nano",
    value: { provider: "openai", model: "gpt-4.1-nano" },
  },
  { label: "GPT 4o", value: { provider: "openai", model: "gpt-4o" } },
  { label: "GPT 4o mini", value: { provider: "openai", model: "gpt-4o-mini" } },
  { label: "Local LLM", value: { provider: "local" } },
];

const AgentConfLlmSelect = () => {
  const { config, setConfig } = useAgentConfigStore();

  return (
    <Select
      value={JSON.stringify({
        provider: config.llm.provider,
        model: config.llm.model,
      })}
      onValueChange={(value) =>
        setConfig({
          ...config,
          llm: JSON.parse(value),
        })
      }
    >
      <SelectTrigger className="w-36">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {llm_models.map((model, i) => (
            <SelectItem key={i} value={JSON.stringify(model.value)}>
              {model.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default AgentConfLlmSelect;
