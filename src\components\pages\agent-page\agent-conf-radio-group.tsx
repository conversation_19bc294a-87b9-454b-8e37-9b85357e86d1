import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import AgentConfTooltip from "./agent-conf-tooltip";

type AgentConfRadioGroupProps = {
  defaultValue?: string;
  options: {
    label: string;
    value: string;
    infoTooltipText?: string;
    slowTooltipText?: string;
  }[];
};

const AgentConfRadioGroup = ({
  options,
  defaultValue,
}: AgentConfRadioGroupProps) => {
  return (
    <RadioGroup defaultValue={defaultValue} className="gap-1">
      {options.map((option) => {
        return (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem value={option.value} id={option.value} />
            <Label htmlFor={option.value} className="opacity-60 text-xs">
              {option.label}
            </Label>
            {option.slowTooltipText && (
              <AgentConfTooltip text={option.slowTooltipText} />
            )}
            {option.infoTooltipText && (
              <AgentConfTooltip type="info" text={option.infoTooltipText} />
            )}
          </div>
        );
      })}
    </RadioGroup>
  );
};

export default AgentConfRadioGroup;
