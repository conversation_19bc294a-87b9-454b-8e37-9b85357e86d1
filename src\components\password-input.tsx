"use client";

import { <PERSON>, <PERSON>Off } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, forwardRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type PasswordInputProps = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  "type"
>;

const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, ...props }, ref) => {
    const t = useTranslations();
    const [show, setShow] = useState(false);
    const [value, setValue] = useState("");

    const checks = [
      {
        id: "length",
        label: t("PasswordInput.passwordChecklist.length"),
        test: (val: string) => val.length >= 6,
      },
      {
        id: "uppercase",
        label: t("PasswordInput.passwordChecklist.uppercase"),
        test: (val: string) => /[A-Z]/.test(val),
      },
      {
        id: "number",
        label: t("PasswordInput.passwordChecklist.number"),
        test: (val: string) => /\d/.test(val),
      },
      {
        id: "special",
        label: t("PasswordInput.passwordChecklist.special"),
        test: (val: string) => /[!@#$%^&*(),.?":{}|<>]/.test(val),
      },
    ];

    const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setValue(e.target.value);
      if (props.onChange) props.onChange(e);
    };

    return (
      <div>
        <div className="relative">
          <Input
            type={show ? "text" : "password"}
            ref={ref}
            className={`pr-10 ${className ?? ""}`}
            {...props}
            value={value}
            onChange={onChange}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setShow(!show)}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-1"
            tabIndex={-1}
            aria-label={
              show
                ? t("PasswordInput.hidePassword")
                : t("PasswordInput.showPassword")
            }
          >
            {show ? <EyeOff size={16} /> : <Eye size={16} />}
          </Button>
        </div>

        <ul className="mt-2 space-y-1 text-sm">
          {checks.map(({ id, label, test }) => {
            const passed = test(value);
            return (
              <li
                key={id}
                className={`flex items-center gap-2 ${
                  passed ? "text-green-600" : "text-gray-500"
                }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-5 w-5 flex-shrink-0 ${
                    passed ? "text-green-600" : "text-gray-400"
                  }`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                  aria-hidden="true"
                >
                  {passed ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M5 13l4 4L19 7"
                    />
                  ) : (
                    <circle cx="12" cy="12" r="10" />
                  )}
                </svg>
                {label}
              </li>
            );
          })}
        </ul>
      </div>
    );
  }
);

PasswordInput.displayName = "PasswordInput";

export default PasswordInput;
