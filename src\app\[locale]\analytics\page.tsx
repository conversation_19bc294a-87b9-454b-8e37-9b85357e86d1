"use client";

import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJ<PERSON>,
  Filler,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from "chart.js";
import {
  CalendarIcon,
  ChartPie,
  Filter,
  Pencil,
  Plus,
  ChevronDown,
  ChevronUp,
  Trash2,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Bar, Doughnut, Line } from "react-chartjs-2";

import AddCustomChartModal from "@/components/pages/analytics-page/add-custom-chart-modal";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import type { DateRange } from "react-day-picker";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

export default function AnalyticsPage() {
  const t = useTranslations();
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2025, 1, 10),
    to: new Date(2025, 6, 7),
  });
  const [timeRange, setTimeRange] = useState("all-time");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTargetChartId, setDeleteTargetChartId] = useState<string | null>(
    null
  );

  const getDateRangeForTimeRange = (range: string): DateRange | undefined => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (range) {
      case "today":
        return {
          from: today,
          to: today,
        };
      case "last-week":
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - 6);
        return {
          from: lastWeekStart,
          to: today,
        };
      case "last-4-weeks":
        const fourWeeksStart = new Date(today);
        fourWeeksStart.setDate(today.getDate() - 27);
        return {
          from: fourWeeksStart,
          to: today,
        };
      case "last-3-months":
        const threeMonthsStart = new Date(today);
        threeMonthsStart.setMonth(today.getMonth() - 3);
        return {
          from: threeMonthsStart,
          to: today,
        };
      case "week-to-date":
        const weekStart = new Date(today);
        const dayOfWeek = today.getDay();
        weekStart.setDate(today.getDate() - dayOfWeek);
        return {
          from: weekStart,
          to: today,
        };
      case "month-to-date":
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return {
          from: monthStart,
          to: today,
        };
      case "year-to-date":
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return {
          from: yearStart,
          to: today,
        };
      case "all-time":
        return {
          from: new Date(2025, 1, 10),
          to: new Date(2025, 6, 7),
        };
      default:
        return undefined;
    }
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    const newDateRange = getDateRangeForTimeRange(range);
    if (newDateRange) {
      setDate(newDateRange);
    }
  };

  const handleDateChange = (newDate: DateRange | undefined) => {
    setDate(newDate);
    if (newDate) {
      setTimeRange("custom");
    }
  };

  const getTimeRangeDisplayText = () => {
    switch (timeRange) {
      case "today":
        return t("analyticsPage.timeRanges.today");
      case "last-week":
        return t("analyticsPage.timeRanges.lastWeek");
      case "last-4-weeks":
        return t("analyticsPage.timeRanges.last4Weeks");
      case "last-3-months":
        return t("analyticsPage.timeRanges.last3Months");
      case "week-to-date":
        return t("analyticsPage.timeRanges.weekToDate");
      case "month-to-date":
        return t("analyticsPage.timeRanges.monthToDate");
      case "year-to-date":
        return t("analyticsPage.timeRanges.yearToDate");
      case "all-time":
        return t("analyticsPage.timeRanges.allTime");
      case "custom":
        return t("analyticsPage.timeRanges.customRange");
      default:
        return t("analyticsPage.timeRanges.allTime");
    }
  };
  const [selectedAgents, setSelectedAgents] = useState([
    "all-agents",
    "single-prompt-test",
    "pegasus-single",
    "pegasus",
    "yapikredi-senaryo",
  ]);

  const agents = [
    { id: "all-agents", name: t("analyticsPage.allAgents"), code: "" },
    {
      id: "single-prompt-test",
      name: "Single-Prompt Agent Test (UI)",
      code: "agent_e40846be2f54ff191bb6aa878",
    },
    {
      id: "pegasus-single",
      name: "Pegasus Single Prompt",
      code: "agent_f9db5a11e9c6373f85374b5094",
    },
    {
      id: "pegasus",
      name: "Pegasus",
      code: "agent_6120824142c43fa7c3bb739019",
    },
    {
      id: "yapikredi-senaryo",
      name: "Yapıkredi Senaryo 2 (Esnek)",
      code: "agent_d4008c773b4a1a204aba716341",
    },
  ];

  const handleAgentToggle = (agentId: string) => {
    if (agentId === "all-agents") {
      setSelectedAgents(
        agentId === selectedAgents[0] ? [] : agents.map((a) => a.id)
      );
    } else {
      setSelectedAgents((prev) =>
        prev.includes(agentId)
          ? prev.filter((id) => id !== agentId)
          : [...prev, agentId]
      );
    }
  };

  const timeLabels = [
    "Feb 10, 2025",
    "Mar 03, 2025",
    "Mar 24, 2025",
    "Apr 14, 2025",
    "May 05, 2025",
    "May 26, 2025",
    "Jun 16, 2025",
    "Jul 07, 2025",
  ];

  const callCountsData = {
    labels: timeLabels,
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callCounts"),
        data: [100, 150, 120, 180, 200, 250, 300, 350],
        borderColor: "rgb(99, 102, 241)",
        backgroundColor: "rgba(99, 102, 241, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const successfulCallsData = {
    labels: [
      t("analyticsPage.chartLabels.successful"),
      t("analyticsPage.chartLabels.unknown"),
      t("analyticsPage.chartLabels.unsuccessful"),
    ],
    datasets: [
      {
        data: [65, 25, 10],
        backgroundColor: [
          "rgb(34, 197, 94)",
          "rgb(156, 163, 175)",
          "rgb(239, 68, 68)",
        ],
        borderWidth: 0,
      },
    ],
  };

  const disconnectionReasonData = {
    labels: [
      t("analyticsPage.disconnectionReasons.dialNoAnswer"),
      t("analyticsPage.disconnectionReasons.agentHangup"),
      t("analyticsPage.disconnectionReasons.callTransfer"),
      t("analyticsPage.disconnectionReasons.userHangup"),
      t("analyticsPage.disconnectionReasons.systemError"),
      t("analyticsPage.disconnectionReasons.networkIssue"),
      t("analyticsPage.disconnectionReasons.timeout"),
    ],
    datasets: [
      {
        data: [45, 20, 15, 10, 5, 3, 2],
        backgroundColor: [
          "rgb(147, 51, 234)",
          "rgb(251, 146, 60)",
          "rgb(34, 197, 94)",
          "rgb(236, 72, 153)",
          "rgb(59, 130, 246)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
        ],
        borderWidth: 0,
      },
    ],
  };

  const userSentimentData = {
    labels: [
      t("analyticsPage.chartLabels.unknown"),
      t("analyticsPage.chartLabels.negative"),
      t("analyticsPage.chartLabels.positive"),
      t("analyticsPage.chartLabels.neutral"),
    ],
    datasets: [
      {
        data: [40, 30, 20, 10],
        backgroundColor: [
          "rgb(59, 130, 246)",
          "rgb(251, 146, 60)",
          "rgb(34, 197, 94)",
          "rgb(156, 163, 175)",
        ],
        borderWidth: 0,
      },
    ],
  };

  const phoneDirectionData = {
    labels: [
      t("analyticsPage.chartLabels.inbound"),
      t("analyticsPage.chartLabels.outbound"),
    ],
    datasets: [
      {
        data: [70, 30],
        backgroundColor: ["rgb(59, 130, 246)", "rgb(251, 146, 60)"],
        borderWidth: 0,
      },
    ],
  };

  const pickupRateData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callPickupRate"),
        data: [75, 80, 85, 70],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const successRateData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callSuccessRate"),
        data: [65, 75, 65, 80],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const transferRateData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callTransferRate"),
        data: [25, 18, 20, 15],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const voicemailRateData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.voicemailRate"),
        data: [25, 35, 30, 40],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const avgDurationData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callDuration"),
        data: [60, 50, 55, 45],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const avgLatencyData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.endToEndLatency"),
        data: [2300, 2400, 2200, 2100],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        fill: true,
        tension: 0.4,
        borderWidth: 1,
        pointRadius: 0,
        pointHoverRadius: 5,
      },
    ],
  };

  const successfulBarData = {
    labels: timeLabels.slice(4),
    datasets: [
      {
        label: t("analyticsPage.chartLabels.callCountsSuccessful"),
        data: [50, 60, 70, 80],
        backgroundColor: "rgb(59, 130, 246)",
      },
      {
        label: t("analyticsPage.chartLabels.callCountsUnknown"),
        data: [20, 25, 30, 35],
        backgroundColor: "rgb(251, 146, 60)",
      },
      {
        label: t("analyticsPage.chartLabels.callCountsUnsuccessful"),
        data: [10, 15, 20, 25],
        backgroundColor: "rgb(34, 197, 94)",
      },
    ],
  };

  const agentBarData = {
    labels: [
      "SunExpress SSS",
      "Yapıkredi Senar...",
      "agent_ea8649c...",
      "Yapıkredi Senar...",
      "agent_e672706...",
      "CeyBer Demo",
    ],
    datasets: [
      {
        data: [95, 85, 80, 75, 65, 60],
        backgroundColor: "rgb(59, 130, 246)",
      },
    ],
  };

  const pickupBarData = {
    labels: [
      "agent_c47e79a...",
      "agent_b6b1227...",
      "agent_d89c835...",
      "agent_bc0603d...",
      "agent_e672706...",
      "Single-Prompt...",
    ],
    datasets: [
      {
        data: [95, 90, 85, 80, 75, 70],
        backgroundColor: "rgb(59, 130, 246)",
      },
    ],
  };

  const transferBarData = {
    labels: ["Pegasus", "Pegasus Single..."],
    datasets: [
      {
        data: [85, 25],
        backgroundColor: "rgb(59, 130, 246)",
      },
    ],
  };

  // const chartOptions = {
  //   responsive: true,
  //   maintainAspectRatio: false,
  //   plugins: {
  //     legend: {
  //       display: false,
  //     },
  //   },
  //   scales: {
  //     x: {
  //       display: true,
  //       grid: {
  //         display: false,
  //       },
  //     },
  //     y: {
  //       display: true,
  //       grid: {
  //         display: true,
  //         color: "rgba(0, 0, 0, 0.1)",
  //       },
  //     },
  //   },
  // };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgb(99, 102, 241)",
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: false,
        callbacks: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          title: function (context: any) {
            return context[0].label;
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          label: function (context: any) {
            const datasetLabel = context.dataset.label || "Value";
            return `${datasetLabel}: ${context.parsed.y}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
    },
    interaction: {
      intersect: false,
      mode: "index" as const,
    },
  };

  const CollapsibleLegend = ({
    data,
    chartId,
    maxVisible = 3,
  }: {
    data: { labels: string[]; datasets: { backgroundColor: string[] }[] };
    chartId: string;
    maxVisible?: number;
  }) => {
    const labels = data.labels || [];
    const colors = data.datasets[0]?.backgroundColor || [];
    const hasMore = labels.length > maxVisible;

    return (
      <div className="mt-4">
        <div className="flex items-center justify-center gap-3 flex-nowrap overflow-hidden">
          {labels.slice(0, maxVisible).map((label: string, index: number) => (
            <div
              key={index}
              className="flex items-center gap-1 text-xs whitespace-nowrap"
            >
              <div
                className="w-2.5 h-2.5 rounded-full flex-shrink-0"
                style={{ backgroundColor: colors[index] }}
              />
              <span className="text-gray-700 truncate">{label}</span>
            </div>
          ))}
        </div>
        {hasMore && (
          <div className="flex justify-center mt-2">
            <Popover>
              <PopoverTrigger asChild>
                <button className="flex items-center gap-1 font-bold cursor-pointer text-sm text-gray-500 hover:text-gray-800 transition-colors">
                  <span>
                    +{labels.length - maxVisible}{" "}
                    {t("analyticsPage.allLegendItems")}
                  </span>
                  <ChevronDown className="w-3 h-3" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="center">
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-2">
                    {labels
                      .slice(maxVisible)
                      .map((label: string, index: number) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 text-sm"
                        >
                          <div
                            className="w-3 h-3 rounded-full flex-shrink-0"
                            style={{
                              backgroundColor: colors[index + maxVisible],
                            }}
                          />
                          <span className="text-gray-700">{label}</span>
                        </div>
                      ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        )}
      </div>
    );
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom" as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
    },
    cutout: "60%",
  };

  const doughnutOptionsWithoutLegend = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    cutout: "60%",
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
      },
      y: {
        display: true,
        grid: {
          display: true,
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
    },
  };

  const horizontalBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: "y" as const,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
      y: {
        display: true,
        grid: {
          display: false,
        },
      },
    },
  };

  const renderDeleteIcon = (chartId: string) =>
    isEditMode ? (
      <button
        className="ml-2 p-1 rounded hover:bg-red-100"
        onClick={() => {
          setDeleteTargetChartId(chartId);
          setShowDeleteModal(true);
        }}
        aria-label="Delete chart"
        type="button"
      >
        <Trash2 className="w-4 h-4 text-red-500" />
      </button>
    ) : null;

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex flex-col items-start">
        <div className="flex gap-2 justify-center items-center ">
          <ChartPie className="text-gray-400 h-4 w-4" />
          <h2 className="text-[16px] font-semibold tracking-tight">
            {t("analyticsPage.title")}
          </h2>
        </div>
        <div className="flex items-center space-x-4 w-full justify-between mt-3 ">
          <div className="flex items-center space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-white justify-start w-[140px]"
                >
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {getTimeRangeDisplayText()}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 flex" align="start">
                <div className="w-48 border-r">
                  <div className="p-4 space-y-2">
                    <button
                      onClick={() => handleTimeRangeChange("today")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "today" ? "bg-blue-50 text-blue-600" : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.today")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("last-week")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "last-week"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.lastWeek")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("last-4-weeks")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "last-4-weeks"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.last4Weeks")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("last-3-months")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "last-3-months"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.last3Months")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("week-to-date")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "week-to-date"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.weekToDate")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("month-to-date")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "month-to-date"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.monthToDate")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("year-to-date")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "year-to-date"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.yearToDate")}
                    </button>
                    <button
                      onClick={() => handleTimeRangeChange("all-time")}
                      className={`w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 ${
                        timeRange === "all-time"
                          ? "bg-blue-50 text-blue-600"
                          : ""
                      }`}
                    >
                      {t("analyticsPage.timeRanges.allTime")}
                    </button>
                  </div>
                </div>
                <div className="p-0">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={date?.from}
                    selected={date}
                    onSelect={handleDateChange}
                    numberOfMonths={2}
                    className="p-4"
                    key={`${date?.from?.getTime()}-${date?.to?.getTime()}`}
                  />
                  <div className="p-4 pt-0 flex items-center justify-between border-t">
                    <span className="text-sm text-muted-foreground">
                      {t("analyticsPage.range")}: {getTimeRangeDisplayText()}
                      {date?.from && date?.to && timeRange !== "all-time" && (
                        <span className="block text-xs text-gray-400 mt-1">
                          {date.from.toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                          })}{" "}
                          -{" "}
                          {date.to.toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                          })}
                        </span>
                      )}
                    </span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        {t("common.cancel")}
                      </Button>
                      <Button size="sm">{t("common.apply")}</Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="bg-white">
                  <Filter className="h-4 w-4 mr-2" />
                  {t("analyticsPage.filter")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-0" align="end">
                <div className="p-4 space-y-4">
                  <div className="relative">
                    <Input
                      placeholder={t("analyticsPage.search")}
                      className="pl-8"
                    />
                    <CalendarIcon className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>

                  <div className="space-y-3">
                    {agents.map((agent) => (
                      <div
                        key={agent.id}
                        className="flex items-center space-x-3"
                      >
                        <Checkbox
                          id={agent.id}
                          checked={selectedAgents.includes(agent.id)}
                          onCheckedChange={() => handleAgentToggle(agent.id)}
                          className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                        />
                        <div className="flex-1">
                          <label
                            htmlFor={agent.id}
                            className="text-sm font-medium cursor-pointer"
                          >
                            {agent.name}
                          </label>
                          {agent.code && (
                            <div className="text-xs text-gray-500">
                              {agent.code}
                            </div>
                          )}
                        </div>
                        {agent.id !== "all-agents" && (
                          <button className="text-gray-400 hover:text-gray-600">
                            <span className="text-sm">›</span>
                          </button>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end gap-2 pt-4 border-t">
                    <Button variant="outline" size="sm">
                      {t("common.cancel")}
                    </Button>
                    <Button
                      size="sm"
                      className="bg-black text-white hover:bg-gray-800"
                    >
                      {t("common.save")}
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex items-center space-x-2  ">
            <Button
              variant="outline"
              size="sm"
              className="bg-white"
              onClick={() => setIsAddModalOpen(true)}
            >
              <span className="mr-2">
                <Plus className="text-gray-400 h-4 w-4" />
              </span>
              {t("analyticsPage.add")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className={`bg-white ${isEditMode ? "ring-2 ring-blue-500" : ""}`}
              onClick={() => setIsEditMode((v) => !v)}
            >
              <span className="mr-2">
                <Pencil className="text-gray-400 h-4 w-4" />
              </span>
              {t("analyticsPage.edit")}
            </Button>
          </div>
        </div>
      </div>

      {/* Top Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analyticsPage.metrics.callCounts")}
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
              {renderDeleteIcon("metric-callCounts")}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3,460</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analyticsPage.metrics.callDuration")}
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
              {renderDeleteIcon("metric-callDuration")}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">01:00</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analyticsPage.metrics.callLatency")}
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
              {renderDeleteIcon("metric-callLatency")}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2328ms</div>
          </CardContent>
        </Card>
      </div>

      {/* First Row Charts */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callCounts")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-callCounts")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={callCountsData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callSuccessful")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-callSuccessful")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Doughnut data={successfulCallsData} options={doughnutOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row Charts */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.disconnectionReason")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-disconnectionReason")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Doughnut
                data={disconnectionReasonData}
                options={doughnutOptionsWithoutLegend}
              />
            </div>
            <CollapsibleLegend
              data={disconnectionReasonData}
              chartId="disconnection-reason"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.userSentiment")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-userSentiment")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Doughnut
                data={userSentimentData}
                options={doughnutOptionsWithoutLegend}
              />
            </div>
            <CollapsibleLegend
              data={userSentimentData}
              chartId="user-sentiment"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.phoneInboundOutbound")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-phoneDirection")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Doughnut data={phoneDirectionData} options={doughnutOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Third Row Charts */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callPickedUpRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-callPickedUpRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={pickupRateData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callSuccessfulRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-callSuccessfulRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={successRateData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callTransferRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-callTransferRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={transferRateData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Fourth Row Charts */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.voicemailRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-voicemailRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={voicemailRateData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.averageCallDuration")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-averageCallDuration")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={avgDurationData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.averageLatency")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("chart-averageLatency")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Line data={avgLatencyData} options={lineChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Fifth Row Charts - Bar Charts */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callSuccessful")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("bar-callSuccessful")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={successfulBarData} options={barOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.disconnectionReason")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("bar-disconnectionReason")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={successfulBarData} options={barOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.userSentiment")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("bar-userSentiment")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={successfulBarData} options={barOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sixth Row Charts - Agent Performance */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callSuccessful")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("agent-callSuccessful")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={agentBarData} options={horizontalBarOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callPickedUpRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("agent-callPickedUpRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={pickupBarData} options={horizontalBarOptions} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">
                {t("analyticsPage.metrics.callTransferRate")}
              </CardTitle>
              <div className="text-xs text-muted-foreground">
                {t("analyticsPage.allAgents")}
              </div>
            </div>
            {renderDeleteIcon("agent-callTransferRate")}
          </CardHeader>
          <CardContent>
            <div className="h-[200px]">
              <Bar data={transferBarData} options={horizontalBarOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-6 min-w-[320px]">
            <div className="mb-4 text-lg font-semibold text-gray-800">
              {t("analyticsPage.deleteConfirmTitle", {
                //@ts-expect-error err
                chart: deleteTargetChartId,
              })}
            </div>
            <div className="mb-6 text-gray-600">
              {t("analyticsPage.deleteConfirmText")}
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
              >
                {t("common.cancel")}
              </Button>
              <Button
                className="bg-red-600 text-white hover:bg-red-700"
                onClick={() => {
                  setShowDeleteModal(false);
                  // TODO: delete logic here
                }}
              >
                {t("common.delete")}
              </Button>
            </div>
          </div>
        </div>
      )}
      <AddCustomChartModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
      />
    </div>
  );
}
