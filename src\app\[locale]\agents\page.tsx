import { ChevronDown } from "lucide-react";
import AgentFolders from "@/components/pages/agents-page/agent-folders";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Link } from "@/i18n";
import { routes } from "@/lib/constants/routes";

const agents = [
  {
    name: "Single-Prompt Agent Test (UI)",
    type: "Single Prompt",
    voice: { name: "<PERSON>", image: "https://i.pravatar.cc/300" },
    phone: "",
    edited: "07/13/2025, 22:27",
  },
  {
    name: "LCWAIKIKI Single Prompt",
    type: "Single Prompt",
    voice: { name: "Osman", image: "" },
    phone: "908504808250",
    edited: "07/11/2025, 05:33",
  },
  {
    name: "Pegasus Single Prompt",
    type: "Single Prompt",
    voice: { name: "Osman", image: "" },
    phone: "+90(850)480-9295",
    edited: "07/07/2025, 11:53",
  },
];

export default function AgentsPage() {
  return (
    <div className="flex h-full p-4 gap-4">
      <Card className="max-w-xs w-full hidden xl:block">
        <CardContent>
          <AgentFolders />
        </CardContent>
      </Card>

      <Card className="w-full">
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-2">
              <h2 className="text-2lg font-semibold">All Agents</h2>
              <div className="flex flex-col lg:flex-row lg:items-center gap-2 w-full lg:w-auto">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="xl:hidden">
                      Folders
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-sm">
                    <DialogHeader>
                      <DialogTitle>FOLDERS</DialogTitle>
                    </DialogHeader>
                    <AgentFolders />
                  </DialogContent>
                </Dialog>

                <Button variant="outline" className="w-full lg:w-auto">
                  Import
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button className="w-full lg:w-auto">
                      Create an Agent <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Voice Agent</DropdownMenuItem>
                    <DropdownMenuItem>Chat Agent</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Input placeholder="Search..." className="w-full lg:w-64" />
              </div>
            </div>

            <div className="w-full overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent Name</TableHead>
                    <TableHead>Agent Type</TableHead>
                    <TableHead>Voice</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Edited by</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {agents.map((agent, i) => (
                    <TableRow className="relative" key={i}>
                      <TableCell>
                        <Link
                          href={{
                            pathname: routes.agent,
                            params: { id: i + 1 },
                          }}
                          className="absolute inset-0 z-10"
                        >
                          {agent.name}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{agent.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            {agent.voice.image ? (
                              <AvatarImage src={agent.voice.image} />
                            ) : (
                              <AvatarFallback>
                                {agent.voice.name[0]}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <span>{agent.voice.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {agent.phone ? (
                          <a
                            href={`tel:${agent.phone}`}
                            className="text-blue-500 underline"
                          >
                            {agent.phone}
                          </a>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>{agent.edited}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
