import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

const AgentFolders = () => {
  return (
    <div>
      <div className="flex justify-between">
        <p>FOLDERS</p>
        <Button variant="outline">
          <Plus />
        </Button>
      </div>
      <Separator className="my-2" />
      <div className="flex flex-col gap-2">
        {["folder1", "folder2"].map((folder) => (
          <Button key={folder} variant="secondary">
            {folder}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default AgentFolders;
