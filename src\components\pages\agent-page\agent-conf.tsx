import {
  Book,
  Box,
  ChartLine,
  Headphones,
  Languages,
  LayoutGrid,
  <PERSON>ting<PERSON>,
  <PERSON><PERSON>he<PERSON>,
  Speech,
} from "lucide-react";
import { useTranslations } from "next-intl";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import AgentConfButton from "./agent-conf-button";
import AgentConfDetail from "./agent-conf-detail";
import AgentConfLlmSelect from "./agent-conf-llm-select";
import AgentConfRadioGroup from "./agent-conf-radio-group";
import AgentConfSlider from "./agent-conf-slider";

const AgentConf = () => {
  const t = useTranslations("AgentConf");

  const accordionItems = [
    {
      icon: LayoutGrid,
      title: t("accordionItems.functions.title"),
      content: (
        <AgentConfDetail
          description={t("accordionItems.functions.description")}
        >
          <AgentConfButton />
        </AgentConfDetail>
      ),
    },
    {
      icon: Book,
      title: t("accordionItems.knowledgeBase.title"),
      content: (
        <AgentConfDetail
          description={t("accordionItems.knowledgeBase.description")}
        >
          <AgentConfButton />
        </AgentConfDetail>
      ),
    },
    {
      icon: Speech,
      title: t("accordionItems.speech.title"),
      content: (
        <div className="flex flex-col gap-4">
          <AgentConfDetail title={t("accordionItems.speech.backgroundSound")}>
            <div className="flex gap-2">
              <Select defaultValue="none">
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="coffeeShop">
                    {t("accordionItems.speech.sounds.coffeeShop")}
                  </SelectItem>
                  <SelectItem value="conventionHall">
                    {t("accordionItems.speech.sounds.conventionHall")}
                  </SelectItem>
                  <SelectItem value="summerOutdoor">
                    {t("accordionItems.speech.sounds.summerOutdoor")}
                  </SelectItem>
                  <SelectItem value="mountainOutdoor">
                    {t("accordionItems.speech.sounds.mountainOutdoor")}
                  </SelectItem>
                  <SelectItem value="staticNoise">
                    {t("accordionItems.speech.sounds.staticNoise")}
                  </SelectItem>
                  <SelectItem value="callCenter">
                    {t("accordionItems.speech.sounds.callCenter")}
                  </SelectItem>
                  <SelectItem value="none">{t("common.none")}</SelectItem>
                </SelectContent>
              </Select>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <Settings />
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <AgentConfDetail
                    title={t("accordionItems.speech.backgroundSoundVolume")}
                  >
                    <AgentConfSlider max={2} defaultValue={1} />
                  </AgentConfDetail>
                </PopoverContent>
              </Popover>
            </div>
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.responsiveness.title")}
            description={t("accordionItems.speech.responsiveness.description")}
            slowTooltipText={t(
              "accordionItems.speech.responsiveness.slowTooltip"
            )}
          >
            <AgentConfSlider max={1} defaultValue={0.8} />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.interruption.title")}
            description={t("accordionItems.speech.interruption.description")}
          >
            <AgentConfSlider max={1} defaultValue={0.8} />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.backchanneling.title")}
            description={t("accordionItems.speech.backchanneling.description")}
          >
            <Switch />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.normalization.title")}
            description={t("accordionItems.speech.normalization.description")}
            slowTooltipText={t(
              "accordionItems.speech.normalization.slowTooltip"
            )}
          >
            <Switch />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.reminder.title")}
            description={t("accordionItems.speech.reminder.description")}
          >
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                className="w-16 text-center no-spinner"
                defaultValue={10}
              />
              <p>{t("common.seconds")}</p>
              <Input
                type="number"
                className="w-16 text-center no-spinner"
                defaultValue={1}
              />
              <p>{t("common.times")}</p>
            </div>
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.speech.pronunciation.title")}
            description={t("accordionItems.speech.pronunciation.description")}
          >
            <AgentConfButton />
          </AgentConfDetail>
        </div>
      ),
    },
    {
      icon: Languages,
      title: t("accordionItems.transcription.title"),
      content: (
        <div className="flex flex-col gap-4">
          <AgentConfDetail
            title={t("accordionItems.transcription.denoising.title")}
            description={t(
              "accordionItems.transcription.denoising.description"
            )}
          >
            <AgentConfRadioGroup
              defaultValue="1"
              options={[
                {
                  label: t(
                    "accordionItems.transcription.denoising.removeNoise"
                  ),
                  value: "1",
                },
                {
                  label: t(
                    "accordionItems.transcription.denoising.removeNoiseAndSpeech"
                  ),
                  value: "2",
                  infoTooltipText: t(
                    "accordionItems.transcription.denoising.tooltip"
                  ),
                },
              ]}
            />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.transcription.mode.title")}
            description={t("accordionItems.transcription.mode.description")}
          >
            <AgentConfRadioGroup
              defaultValue="1"
              options={[
                {
                  label: t("accordionItems.transcription.mode.speed"),
                  value: "1",
                },
                {
                  label: t("accordionItems.transcription.mode.accuracy"),
                  value: "2",
                  slowTooltipText: t(
                    "accordionItems.transcription.mode.latencyWarning"
                  ),
                  infoTooltipText: t(
                    "accordionItems.transcription.mode.accuracyInfo"
                  ),
                },
              ]}
            />
          </AgentConfDetail>

          <AgentConfDetail
            title={t("accordionItems.transcription.keywords.title")}
            description={t("accordionItems.transcription.keywords.description")}
          >
            <Input />
          </AgentConfDetail>
        </div>
      ),
    },
    {
      icon: Headphones,
      title: t("accordionItems.call.title"),
      content: (
        <div className="flex flex-col gap-4">
          <AgentConfDetail
            title={t("accordionItems.call.voicemail.title")}
            description={t("accordionItems.call.voicemail.description")}
          >
            <Switch />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.call.keypad.title")}
            description={t("accordionItems.call.keypad.description")}
          >
            <Switch />
          </AgentConfDetail>
          <AgentConfDetail
            description={t("accordionItems.call.conditions.description")}
          >
            <Card>
              <CardContent>
                <AgentConfDetail
                  title={t("accordionItems.call.timeout.title")}
                  description={t("accordionItems.call.timeout.description")}
                >
                  <AgentConfSlider
                    min={1}
                    max={15}
                    defaultValue={2.5}
                    step={0.5}
                    hasTimeUnit
                  />
                </AgentConfDetail>
                <AgentConfDetail
                  title={t("accordionItems.call.terminationKey.title")}
                  description={t(
                    "accordionItems.call.terminationKey.description"
                  )}
                >
                  <Switch />
                </AgentConfDetail>
                <AgentConfDetail
                  title={t("accordionItems.call.digitLimit.title")}
                  description={t("accordionItems.call.digitLimit.description")}
                >
                  <Switch />
                </AgentConfDetail>
              </CardContent>
            </Card>
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.call.silence.title")}
            description={t("accordionItems.call.silence.description")}
          >
            <AgentConfSlider
              min={10}
              max={1800}
              defaultValue={30}
              hasTimeUnit
            />
          </AgentConfDetail>
          <AgentConfDetail title={t("accordionItems.call.maxDuration.title")}>
            <AgentConfSlider max={7200} defaultValue={180} hasTimeUnit />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.call.pauseBeforeSpeaking.title")}
            description={t(
              "accordionItems.call.pauseBeforeSpeaking.description"
            )}
          >
            <AgentConfSlider max={5} defaultValue={0} />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.call.ringDuration.title")}
            description={t("accordionItems.call.ringDuration.description")}
          >
            <AgentConfSlider max={90} defaultValue={30} />
          </AgentConfDetail>
        </div>
      ),
    },
    {
      icon: ChartLine,
      title: t("accordionItems.analysis.title"),
      content: (
        <AgentConfDetail
          title={t("accordionItems.analysis.dataRetrieval.title")}
          description={t("accordionItems.analysis.dataRetrieval.description")}
        >
          <div className="flex justify-between">
            <AgentConfButton />
            <AgentConfLlmSelect />
          </div>
        </AgentConfDetail>
      ),
    },
    {
      icon: ShieldCheck,
      title: t("accordionItems.security.title"),
      content: (
        <div className="flex flex-col gap-4">
          <AgentConfDetail
            title={t("accordionItems.security.optOut.title")}
            description={t("accordionItems.security.optOut.description")}
          >
            <Switch />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.security.optInUrls.title")}
            description={t("accordionItems.security.optInUrls.description")}
          >
            <Switch />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.security.fallbackVoice.title")}
            description={t("accordionItems.security.fallbackVoice.description")}
          >
            <AgentConfButton />
          </AgentConfDetail>
          <AgentConfDetail
            title={t("accordionItems.security.defaultVars.title")}
            description={t("accordionItems.security.defaultVars.description")}
          >
            <AgentConfButton type="set" />
          </AgentConfDetail>
        </div>
      ),
    },
    {
      icon: Box,
      title: t("accordionItems.webhook.title"),
      content: (
        <AgentConfDetail
          title={t("accordionItems.webhook.urlTitle")}
          description={t("accordionItems.webhook.urlDescription")}
        >
          <Input />
        </AgentConfDetail>
      ),
    },
  ];

  return (
    <Accordion type="single" collapsible>
      {accordionItems.map((item, i) => (
        <AccordionItem key={i} value={i.toString()}>
          <AccordionTrigger>
            <div className="flex gap-2 items-center">
              <item.icon size={16} />
              <p>{item.title}</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>{item.content}</AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};

export default AgentConf;
