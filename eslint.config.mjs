import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "@typescript-eslint/no-empty-object-type": "warn",
      "no-unused-vars": "off",
      "@typescript-eslint/no-restricted-types": ["warn"],
      "@typescript-eslint/no-unused-vars": ["warn"],
      "no-use-before-define": "off",
      "@typescript-eslint/no-use-before-define": ["off"],
      "react/self-closing-comp": [
        "error",
        {
          component: true,
          html: true,
        },
      ],
      "no-restricted-imports": [
        "error",
        {
          paths: [
            {
              name: "next/navigation",
              importNames: ["useRouter", "usePathname", "redirect"],
              message:
                "Use 'useRouter', 'usePathname' and 'redirect' from @/i18n instead.",
            },
            {
              name: "next/router",
              importNames: ["useRouter"],
              message: "Use 'useRouter' from @/i18n instead.",
            },
            {
              name: "next/link",
              importNames: ["default"],
              message: "Use Link from @/i18n instead.",
            },
          ],
        },
      ],
      "@typescript-eslint/consistent-type-imports": [
        "error",
        {
          prefer: "type-imports",
        },
      ],
      "import/order": [
        "error",
        {
          groups: [
            "builtin",
            "external",
            "parent",
            "sibling",
            "index",
            "object",
            "type",
          ],
          pathGroups: [
            {
              pattern: "@/**/**",
              group: "parent",
              position: "before",
            },
          ],
          alphabetize: {
            order: "asc",
          },
        },
      ],
    },
  },
];

export default eslintConfig;
