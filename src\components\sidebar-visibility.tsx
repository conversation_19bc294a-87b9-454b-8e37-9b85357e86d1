"use client";
import { useSelectedLayoutSegment } from "next/navigation";
import { SidebarWrapper } from "@/components/sidebar-wrapper";
import type { ReactNode } from "react";

export default function SidebarVisibility({
  children,
}: {
  children: ReactNode;
}) {
  const segment = useSelectedLayoutSegment();
  if (segment === "login") {
    return <>{children}</>;
  }
  return <SidebarWrapper>{children}</SidebarWrapper>;
}
