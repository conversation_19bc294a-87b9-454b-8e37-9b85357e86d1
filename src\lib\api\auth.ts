import {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '@/lib/types/auth';

const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';
const REFRESH_TOKEN_ENDPOINT = 'https://placeholder-refresh-endpoint.com/api/Auth/refresh'; // Placeholder

class AuthApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'AuthApiError';
  }
}

async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorCode = response.status.toString();

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
      errorCode = errorData.code || errorCode;
    } catch {
      // If response is not JSON, use default error message
    }

    throw new AuthApiError(errorMessage, response.status, errorCode);
  }

  return response.json();
}

export const authApi = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiRequest<LoginResponse>(
        `${API_BASE_URL}/Auth/authenticate-user`,
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        }
      );

      return response;
    } catch (error) {
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during login');
    }
  },

  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    try {
      const response = await apiRequest<RefreshTokenResponse>(
        REFRESH_TOKEN_ENDPOINT,
        {
          method: 'POST',
          body: JSON.stringify(request),
        }
      );

      return response;
    } catch (error) {
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during token refresh');
    }
  },

  async logout(token: string): Promise<void> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/logout`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      // Logout should not fail the client-side logout process
      console.warn('Server logout failed:', error);
    }
  },

  async validateToken(token: string): Promise<boolean> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/validate`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      return false;
    }
  },
};

export { AuthApiError };
