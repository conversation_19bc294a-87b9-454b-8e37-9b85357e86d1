import type { LocalizationKeys } from "@/i18n";

const tr: LocalizationKeys = {
  title: "tr title",
  common: {
    cancel: "İpta<PERSON>",
    apply: "<PERSON><PERSON><PERSON><PERSON>",
    save: "<PERSON><PERSON>",
    previous: "<PERSON><PERSON><PERSON>",
    next: "<PERSON><PERSON><PERSON>",
    showMore: "<PERSON>ha fazla göster",
    showLess: "<PERSON>ha az göster",
    add: "<PERSON><PERSON>",
    delete: "Sil",
    eg: "<PERSON>rne<PERSON>",
    learnMore: "Daha fazla bilgi",
    current: "Varsayılan",
  },
  sidebar: {
    agents: "Modeller",
    knowledgeBase: "Bilgi Tabanı",
    phoneNumbers: "Telefon Numaraları",
    batchCall: "Toplu Arama",
    callHistory: "Arama Geçmişi",
    analytics: "Ana<PERSON>z",
    billing: "Faturalama",
    keys: "API Anahtarları",
    webhooks: "Webhook'lar",
    settings: "Ayarlar",
    helpCenter: "Yardım Merkezi",
    logOut: "<PERSON>ık<PERSON>ş Yap",
    toggleSidebar: "Kenar Çubuğunu Aç/Kapat",
  },
  phoneNumbersPage: {
    title: "Telefon Numaraları",
    buyNewNumber: "Yeni Numara Satın Al",
    buyPhoneNumber: "Telefon Numarası Satın Al",
    phoneNumber: "Telefon Numarası",
    provider: "Sağlayıcı",
    areaCode: "Alan Kodu",
    areaCodePlaceholder: "Örnek: 650",
    optional: "İsteğe Bağlı",
    monthlyFee: "Bu numaranın aylık maliyeti",
    connectToYourNumberViaSIPTrunking: "SIP trunking ile numaranıza bağlanın",
    phoneNumberPlaceholder: "Telefon Numarası giriniz",
    terminationURI: "Termination URI",
    terminationURIPlaceholder: "Termination URI giriniz (SIP server uri değil)",
    sipTrunkUserName: "SIP Trunk Kullanıcı Adı",
    sipTrunkUserNamePlaceholder: "SIP Trunk Kullanıcı Adı giriniz",
    sipTrunkPassword: "SIP Trunk Şifresi",
    sipTrunkPasswordPlaceholder: "SIP Trunk Şifresi giriniz",
    nickname: "Nickname",
    nicknamePlaceholder: "Nickname giriniz",
    inboundCallAgent: "Gelen Çağrı Agent'ı",
    outboundCallAgent: "Giden Çağrı Agent'ı",
    makeAnOutboundCall: "Giden Çağrı Yap",
    makeOutboundCall: "Giden Çağrı Yap",
    internationalCalling: "Uluslararası çağrı yapmak istiyorsanız, lütfen özel bir telefon sağlayıcısı kullanın",
    seeDetails: "Detayları Gör",
    customSipHeaders: "Özel SIP Headers",
    addKeyValuePairs: "Çağrı yönlendirme, meta veri veya operatör entegrasyonu için anahtar/değer çiftleri ekleyin.",
    xHeaderName: "X-Header Adı",
    value: "Değer",
    call: "Arama Yap",
    noneDisableInbound: "Yok (girişi devre dışı bırak)",
    noneDisableOutbound: "Yok (çıkışı devre dışı bırak)",
    addAnInboundWebhook: "Gelen webhook ekle",
    enterURL: "URL giriniz",
  },
  callHistoryPage: {
    title: "Arama Geçmişi",
    dateRange: "Tarih Aralığı",
    filter: "Filtrele",
    filterByAgent: "Agent'a Göre Filtrele",
    agent: "Agent",
    noAgentFound: "Agent bulunamadı",
    agentsSelected: "agent seçildi",
    filterByCallId: "Arama ID'ye Göre Filtrele",
    enterCallId: "Arama ID giriniz",
    customizeField: "Alanları Özelleştir",
    export: "Dışa Aktar",
    time: "Zaman",
    duration: "Süre",
    channelType: "Kanal Tipi",
    cost: "Maliyet",
    sessionId: "Oturum ID",
    endSession: "Oturum Sonu",
    sessionStatus: "Oturum Durumu",
    userSentiment: "Kullanıcı Duygusu",
    agentId: "Agent ID",
    agentVersion: "Agent Versiyonu",
    agentName: "Agent Adı",
    from: "Kimden",
    to: "Kime",
    sessionOutcome: "Oturum Sonucu",
    endToEndLatency: "Uçtan Uca Gecikme",
    searchAgent: "Agent ara...",
    phoneCall: "Telefon Araması",
    webCall: "Web Araması",
    conversationAnalysis: "Konuşma Analizi",
    preset: "Ön Ayar",
    callSuccessful: "Arama Başarılı",
    successful: "Başarılı",
    unsuccessful: "Başarısız",
    callStatus: "Arama Durumu",
    disconnectionReason: "Bağlantı Kesilme Nedeni",
    summary: "Özet",
    transcription: "Transkript",
    noTranscription: "Transkript yok",
    data: "Veri",
    noDynamicVariables: "Dinamik Değişken yok",
    noExtractedDynamicVariables: "Çıkarılmış Dinamik Değişken yok",
    detailLogs: "Detay Logları",
  },
  app: {
    title: "Voice AI",
    subtitle: "Akıllı Konuşmalar",
    language: "Dil",
    selectLanguage: "Dil Seçin",
  },
  AgentConf: {
    accordionItems: {
      functions: {
        title: "Fonksiyonlar",
        description:
          "Ajana takvim rezervasyonu, çağrı sonlandırma gibi yetenekler kazandırın.",
      },
      knowledgeBase: {
        title: "Bilgi Tabanı",
        description: "Ajana bağlam sağlamak için bilgi tabanı ekleyin.",
      },
      speech: {
        title: "Konuşma Ayarları",
        backgroundSound: "Arka Plan Sesi",
        backgroundSoundVolume: "Arka Plan Ses Seviyesi",
        responsiveness: {
          title: "Tepki Hızı",
          description:
            "Kullanıcı konuşmayı bitirdikten sonra ajanın ne kadar hızlı yanıt verdiğini kontrol edin.",
          slowTooltip: "Yavaş ayarlanırsa gecikme artar",
        },
        interruption: {
          title: "Kesinti Hassasiyeti",
          description:
            "Ajana yapılan insan konuşması kesintilerine ne kadar duyarlı olacağını kontrol edin.",
        },
        backchanneling: {
          title: "Geri Bildirim Aç",
          description:
            "Ajanın konuşma sırasında 'evet', 'hı-hı' gibi geri bildirimlerle aktif dinleme yapmasını sağlar.",
        },
        normalization: {
          title: "Konuşma Normalizasyonunu Aç",
          description:
            "Sayı, para birimi, tarih gibi öğeleri doğal konuşma biçimlerine dönüştürür. (Daha fazla bilgi)",
          slowTooltip: "Bu ayar 100ms gecikmeye neden olur",
        },
        reminder: {
          title: "Hatırlatma Mesajı Sıklığı",
          description:
            "Ajanın ne sıklıkla hatırlatma mesajı göndereceğini ayarlayın.",
        },
        pronunciation: {
          title: "Telaffuz",
          description:
            "Modelin belirli kelimeleri veya isimleri nasıl telaffuz edeceğini yönlendirin. (Daha fazla bilgi)",
        },
        sounds: {
          coffeeShop: "Kafe",
          conventionHall: "Konferans Salonu",
          summerOutdoor: "Yaz Ortamı",
          mountainOutdoor: "Dağ Ortamı",
          staticNoise: "Statik Gürültü",
          callCenter: "Çağrı Merkezi",
        },
      },
      transcription: {
        title: "Transkripsiyon Ayarları",
        denoising: {
          title: "Gürültü Azaltma Modu",
          description:
            "İstenmeyen arka plan seslerini veya konuşmaları filtreleyin. (Daha fazla bilgi)",
          removeNoise: "Gürültüyü kaldır",
          removeNoiseAndSpeech: "Gürültü + arka plan konuşmasını kaldır",
          tooltip:
            "Televizyon sesi gibi arka plan insan konuşmalarını agresif şekilde kaldırır, doğruluğu azaltabilir.",
        },
        mode: {
          title: "Transkripsiyon Modu",
          description:
            "Hız ve doğruluk arasında denge kurun. (Daha fazla bilgi)",
          speed: "Hıza göre optimize et",
          accuracy: "Doğruluğa göre optimize et",
          latencyWarning: "Bu ayar 200ms gecikmeye neden olabilir",
          accuracyInfo:
            "Sayılarda ve tarihlerde daha iyi performans gösterir, konuşma kesintilerini azaltır.",
        },
        keywords: {
          title: "Anahtar Kelimeler",
          description:
            "Modelin kelime dağarcığını genişletmek için özelleştirilmiş kelimeler tanımlayın.",
        },
      },
      call: {
        title: "Çağrı Ayarları",
        voicemail: {
          title: "Sesli Mesaj Algılama",
          description:
            "Sesli mesaj algılanırsa çağrıyı sonlandırın veya mesaj bırakın.",
        },
        keypad: {
          title: "Kullanıcı Tuş Girişi Algılama",
          description:
            "Ajanın çağrı sırasında tuş girişlerini dinlemesini etkinleştirin.",
        },
        conditions: {
          description:
            "Aşağıdaki koşullardan biri gerçekleştiğinde ajan yanıt verir:",
        },
        timeout: {
          title: "Zaman Aşımı",
          description: "Belirli sürede tuş girişi olmazsa ajan yanıt verir.",
        },
        terminationKey: {
          title: "Sonlandırma Tuşu",
          description:
            "Kullanıcı belirli bir tuşa bastığında ajan yanıt verir.",
        },
        digitLimit: {
          title: "Basamak Sınırı",
          description:
            "Kullanıcı belirli sayıda rakam girdikten sonra ajan yanıt verir.",
        },
        silence: {
          title: "Sessizlikte Çağrıyı Bitir",
          description: "Kullanıcı uzun süre sessiz kalırsa çağrıyı sonlandır.",
        },
        maxDuration: {
          title: "Maksimum Çağrı Süresi",
        },
        pauseBeforeSpeaking: {
          title: "Konuşma Öncesi Gecikme",
          description: "Ajanın çağrı başında konuşmaya başlamadan önceki süre.",
        },
        ringDuration: {
          title: "Zil Süresi",
          description:
            "Cevaplanmayan çağrı olarak işaretlenmeden önceki maksimum zil süresi.",
        },
      },
      analysis: {
        title: "Görüşme Sonrası Analiz",
        dataRetrieval: {
          title: "Görüşme Verisi Çekme",
          description:
            "Sesten hangi bilgilerin çıkarılacağını tanımlayın. (Daha fazla bilgi)",
        },
      },
      security: {
        title: "Güvenlik ve Yedek Ayarlar",
        optOut: {
          title: "Hassas Veri Saklamayı Devre Dışı Bırak",
          description:
            "Retell'in hassas verileri saklayıp saklamayacağını kontrol edin. (Daha fazla bilgi)",
        },
        optInUrls: {
          title: "Güvenli URL'leri Etkinleştir",
          description:
            "URL'lere güvenlik imzası ekleyin. 24 saat sonra sona erer. (Daha fazla bilgi)",
        },
        fallbackVoice: {
          title: "Yedek Ses Kimliği",
          description:
            "Geçerli ses sağlayıcısı başarısız olursa, çağrıya devam etmek için yedek ses atayın.",
        },
        defaultVars: {
          title: "Varsayılan Dinamik Değişkenler",
          description:
            "Tüm uç noktalar için sağlanmadığında kullanılacak yedek değerleri ayarlayın.",
        },
      },
      webhook: {
        title: "Webhook Ayarları",
        urlTitle: "Ajan Seviyesi Webhook URL'si",
        urlDescription:
          "Olayları almak için webhook URL'si. (Daha fazla bilgi)",
      },
    },
    common: {
      add: "Ekle",
      setUp: "Kur",
      none: "Yok",
      seconds: "saniye",
      times: "kez",
      shortSecond: "sn",
      shortMinute: "d",
      shortHour: "s",
    },
  },
  analyticsPage: {
    title: "Analiz",
    timeRanges: {
      today: "Bugün",
      lastWeek: "Son 1 hafta",
      last4Weeks: "Son 4 hafta",
      last3Months: "Son 3 ay",
      weekToDate: "Haftanın başından beri",
      monthToDate: "Ayın başından beri",
      yearToDate: "Yılın başından beri",
      allTime: "Tüm zamanlar",
      customRange: "Özel aralık",
    },
    filter: "Filtrele",
    add: "Ekle",
    edit: "Düzenle",
    search: "Ara...",
    allAgents: "Tüm modeller",
    range: "Aralık",
    metrics: {
      callCounts: "Arama Sayıları",
      callDuration: "Arama Süresi",
      callLatency: "Arama Gecikmesi",
      callSuccessful: "Başarılı Aramalar",
      disconnectionReason: "Bağlantı Kesme Nedeni",
      userSentiment: "Kullanıcı Duygusu",
      phoneInboundOutbound: "Telefon gelen/giden",
      callPickedUpRate: "Arama Cevaplama Oranı",
      callSuccessfulRate: "Arama Başarı Oranı",
      callTransferRate: "Arama Transfer Oranı",
      voicemailRate: "Sesli Mesaj Oranı",
      averageCallDuration: "Ortalama arama süresi",
      averageLatency: "Ortalama Gecikme",
    },
    chartLabels: {
      successful: "Başarılı",
      unknown: "Bilinmeyen",
      unsuccessful: "Başarısız",
      inbound: "Gelen",
      outbound: "Giden",
      negative: "Olumsuz",
      positive: "Olumlu",
      neutral: "Nötr",
      callCounts: "Arama sayıları",
      callPickupRate: "Arama Cevaplama Oranı",
      callSuccessRate: "Arama Başarı Oranı",
      callTransferRate: "Arama Transfer Oranı",
      voicemailRate: "Sesli mesaj oranı",
      callDuration: "Arama süresi",
      endToEndLatency: "Uçtan uca gecikme",
      callCountsSuccessful: "Arama sayıları: başarılı",
      callCountsUnknown: "Arama sayıları: bilinmeyen",
      callCountsUnsuccessful: "Arama sayıları: başarısız",
    },
    disconnectionReasons: {
      dialNoAnswer: "Arama cevapsız",
      agentHangup: "Model kapattı",
      callTransfer: "Arama transferi",
      userHangup: "Kullanıcı kapattı",
      systemError: "Sistem hatası",
      networkIssue: "Ağ sorunu",
      timeout: "Zaman aşımı",
    },
    allLegendItems: "Daha",
    deleteConfirmTitle: "Bu grafiği silmek istediğinize emin misiniz?",
    deleteConfirmText:
      "Bu işlem geri alınamaz. Grafik kalıcı olarak silinecek.",
  },
  Auth: {
    login: "Giriş Yap",
    register: "Kayıt Ol",
    forgotPassword: "Şifrenizi mi unuttunuz?",
    backToLogin: "← Girişe Dön",
    email: "E-posta",
    password: "Şifre",
    name: "İsim",
    resetTitle: "Şifre Sıfırlama",
    resetButton: "Sıfırlama Linki Gönder",
    nameError: "İsim en az 2 karakter olmalıdır",
    emailError: "Lütfen geçerli bir e-posta adresi girin",
  },
  PasswordInput: {
    password: "Şifre",
    showPassword: "Şifreyi göster",
    hidePassword: "Şifreyi gizle",
    passwordChecklist: {
      length: "En az 6 karakter",
      uppercase: "En az bir büyük harf",
      number: "En az bir sayı",
      special: "En az bir özel karakter",
    },
  },
};

export default tr;
