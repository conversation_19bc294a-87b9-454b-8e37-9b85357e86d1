"use client";

import { format, parse } from "date-fns";
import { History, Plus, Settings, ArrowUpFromLine, ChevronLeft, ChevronRight, Calendar as CalendarIcon, X, Copy, Check, Download, SquareCheck, Headset, Phone, Gauge, MicVocal, Tag } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import type { DateRange } from "react-day-picker";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function CallHistoryPage() {
  const t = useTranslations();
  
  // create dummy data for table
  const endSessionTypes = ["user hangup", "agent hangup", "dial busy", "dial failed", "dial no answer", "inactivity"];
  const userSentiments = ["Neutral", "Positive", "Negative", "Unknown"];
  const sessionStatuses = ["ended", "not_connected", "error"];
  const sessionOutcomes = ["Successful", "Unsuccessful"];
  const agentNames = ["AgentX", "AgentY", "AgentZ", "AgentAlpha", "AgentBeta"];

  const data = Array.from({ length: 30 }).map((_, i) => ({
    sessionId: `sess_${(i + 1).toString().padStart(3, "0")}`,
    time: `${((i % 31) + 1).toString().padStart(2, "0")}/07/2025`,
    endSession: endSessionTypes[i % endSessionTypes.length],
    duration: "0:10",
    channelType: i % 2 === 0 ? "web_call" : "phone_call",
    sessionStatus: sessionStatuses[i % sessionStatuses.length],
    userSentiment: userSentiments[i % 4],
    agentId: `agent_${(100 + i * 25).toString().padStart(3, "0")}`,
    agentVersion: `v${1 + (i % 3)}.${(i % 10)}.${(i % 5)}`,
    agentName: agentNames[i % agentNames.length],
    from: i % 2 === 0 ? "+905551234567" : `user${i}@example.com`,
    to: i % 2 === 0 ? "+905559876543" : "<EMAIL>",
    sessionOutcome: sessionOutcomes[i % 2],
    endToEndLatency: `${120 + i * 5}ms`,
    cost: `$${(0.019 + i * 0.01).toFixed(3)}`,
    audioUrl: `https://www.soundhelix.com/examples/mp3/SoundHelix-Song-${(i % 3) + 1}.mp3`,
    tenantName: i % 2 === 0 ? "DEMO" : "TESTTENANT",
    autoAnalysis: i % 3 === 0 ? true : false,
    realAgentId: 6500 + i,
  }));

  const allColumns = [
    { key: "time", label: t("callHistoryPage.time") },
    { key: "duration", label: t("callHistoryPage.duration") },
    { key: "channelType", label: t("callHistoryPage.channelType") },
    { key: "cost", label: t("callHistoryPage.cost") },
    { key: "sessionId", label: t("callHistoryPage.sessionId") },
    { key: "endSession", label: t("callHistoryPage.endSession") },
    { key: "sessionStatus", label: t("callHistoryPage.sessionStatus") },
    { key: "userSentiment", label: t("callHistoryPage.userSentiment") },
    { key: "agentId", label: t("callHistoryPage.agentId") },
    { key: "agentVersion", label: t("callHistoryPage.agentVersion") },
    { key: "agentName", label: t("callHistoryPage.agentName") },
    { key: "from", label: t("callHistoryPage.from") },
    { key: "to", label: t("callHistoryPage.to") },
    { key: "sessionOutcome", label: t("callHistoryPage.sessionOutcome") },
    { key: "endToEndLatency", label: t("callHistoryPage.endToEndLatency") },
  ];

  const [visibleColumns, setVisibleColumns] = useState(allColumns.map(col => col.key));
  const [showCustomizePopover, setShowCustomizePopover] = useState(false);
  const [pendingColumns, setPendingColumns] = useState(visibleColumns);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;
  const [copiedSessionId, setCopiedSessionId] = useState<string | null>(null);
  const [selectedColumnId, setSelectedColumnId] = useState<string | null>(null);

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [showDateRangePopover, setShowDateRangePopover] = useState(false);

  // parse date from string
  const parseDate = (str: string) => {
    return parse(str, "dd/MM/yyyy", new Date());
  };

  // filter data by date range
  let filteredData = dateRange?.from && dateRange?.to
    ? data.filter(row => {
        const d = parseDate(row.time);
        return d >= dateRange.from! && d <= dateRange.to!;
      })
    : data;

    const filterOptions = [
      { key: "agent", label: "Agent" },
      { key: "callId", label: "Call ID" },
    ];
    const [showFilterPopover, setShowFilterPopover] = useState(false);
    const [showAgentFilterPopover, setShowAgentFilterPopover] = useState(false);
    
    // Agent filter states
    const [agentSearch, setAgentSearch] = useState("");
    const [pendingAgents, setPendingAgents] = useState<string[]>([]);
  
    // get unique agents from data
    const uniqueAgents = Array.from(new Map(data.map(agent => [agent.agentId, agent])).values());

    // filter agents by search
    const filteredAgents = uniqueAgents.filter(agent =>
      agent.agentName.toLowerCase().includes(agentSearch.toLowerCase()) ||
      agent.agentId.toLowerCase().includes(agentSearch.toLowerCase())
    );
    // sort selected agents to top
    const sortedAgents = [
      ...filteredAgents.filter(agent => pendingAgents.includes(agent.agentId)),
      ...filteredAgents.filter(agent => !pendingAgents.includes(agent.agentId)),
    ];

  // filter agents on table
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  if (selectedAgents.length > 0) {
    filteredData = filteredData.filter(row => selectedAgents.includes(row.agentId));
  }

  // Call ID filter logic
  const [showCallIdFilterPopover, setShowCallIdFilterPopover] = useState(false);
  const [callIdInput, setCallIdInput] = useState("");
  const [selectedCallId, setSelectedCallId] = useState("");
  if (selectedCallId) {
    filteredData = filteredData.filter(row => row.sessionId === selectedCallId);
  }

  // pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const handleCustomizeOpen = () => {
    setPendingColumns(visibleColumns);
    setShowCustomizePopover(true);
  };

  const handleCustomizeCancel = () => setShowCustomizePopover(false);

  const handleCustomizeSave = () => {
    setVisibleColumns(pendingColumns);
    setShowCustomizePopover(false);
  };
  
  const handleColumnToggle = (key: string) => {
    setPendingColumns(cols =>
      cols.includes(key) ? cols.filter(c => c !== key) : [...cols, key]
    );
  };

  // Pagination logic
  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  // Copy session ID function
  const handleCopySessionId = async (sessionId: string) => {
    try {
      await navigator.clipboard.writeText(sessionId);
      setCopiedSessionId(sessionId);
      setTimeout(() => {
        setCopiedSessionId(null);
      }, 3000);
    } catch (err) {
      console.error('Failed to copy session ID:', err);
    }
  };

  // define cell content for various columns
  const renderCellContent = (col: { key: string; label: string }, row: Record<string, unknown>) => {
    switch (col.key) {
      case "sessionId":
        return (
          <div className="flex items-center gap-2">
            <span>{row.sessionId as string}</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
              onClick={e => {
                e.stopPropagation();
                handleCopySessionId(row.sessionId as string);
              }}
            >
              {copiedSessionId === row.sessionId ? (
                <Check className="h-3 w-3 text-green-600" />
              ) : (
                <Copy className="h-3 w-3 text-gray-500" />
              )}
            </Button>
          </div>
        );
      
      case "endSession":
        return (
          <>
            <span
              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                row.endSession === "user hangup" || row.endSession === "agent hangup"
                  ? "bg-green-600"
                  : "bg-red-600"
              }`}
            />
            {row.endSession}
          </>
        );
      
      case "sessionStatus":
        return (
          <>
            <span
              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                row.sessionStatus === "error" ? "bg-red-600" : "bg-gray-300"
              }`}
            />
            {row.sessionStatus}
          </>
        );
      
      case "userSentiment":
        return (
          <>
            <span
              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                row.userSentiment === "Positive"
                  ? "bg-green-600"
                  : row.userSentiment === "Neutral"
                  ? "bg-blue-600"
                  : "bg-red-600"
              }`}
            />
            {row.userSentiment}
          </>
        );
      
      case "sessionOutcome":
        return (
          <>
            <span
              className={`inline-block w-2 h-2 rounded-full mr-2 ${
                row.sessionOutcome === "Successful"
                  ? "bg-green-600"
                  : "bg-red-600"
              }`}
            />
            {row.sessionOutcome}
          </>
        );
      
      default:
        return row[col.key] as React.ReactNode;
    }
  };

  return (
    <div className="flex flex-col w-full space-y-4 p-4 md:p-8 pt-6 pb-16 overflow-hidden max-h-[100vh]">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5" />
          <h2 className="text-xl font-bold tracking-tight">{t("callHistoryPage.title")}</h2>
        </div>
        <div />
      </div>
      <div className="flex justify-between items-center gap-4 mt-4">
        <div className="flex gap-4">
          {/* Date range button */}
          <Popover open={showDateRangePopover} onOpenChange={setShowDateRangePopover}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 px-5 py-4 text-gray-500 font-semibold cursor-pointer"
                onClick={() => setShowDateRangePopover(true)}
              >
                <CalendarIcon className="h-5 w-5" />
                {dateRange?.from && dateRange?.to
                  ? `${format(dateRange.from, "MM/dd/yyyy")} - ${format(dateRange.to, "MM/dd/yyyy")}`
                  : t("callHistoryPage.dateRange")}
              </Button>
            </PopoverTrigger>
            {/* date range popover calendar content */}
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          {/* Agent filter chip */}
          {selectedAgents.length > 0 && (
            <div className="flex items-center bg-gray-50 rounded px-2 h-10 ml-2">
              <span className="text-sm text-gray-700 mr-1">{t("callHistoryPage.agent")}</span>
              {selectedAgents.length === 1 ? (
                <a
                  href="#"
                  className="text-sm text-blue-600 mr-1 max-w-[180px] truncate inline-block"
                  title={uniqueAgents.find(a => a.agentId === selectedAgents[0])?.agentName}
                >
                  {uniqueAgents.find(a => a.agentId === selectedAgents[0])?.agentName}
                </a>
              ) : (
                <span className="text-sm text-blue-600 mr-1">{selectedAgents.length} {t("callHistoryPage.agentsSelected")}</span>
              )}
              <Button
                variant="ghost"
                className="p-0 w-6 h-6 hover:bg-gray-200 transition-colors flex items-center justify-center"
                onClick={() => {
                  setSelectedAgents([]);
                  setPendingAgents([]);
                }}
              >
                <X className="h-3 w-3 text-gray-500" />
              </Button>
            </div>
          )}
          {/* Call ID filter chip */}
          {selectedCallId && (
            <div className="flex items-center bg-gray-50 rounded px-2 h-10 ml-2">
              <span className="text-sm text-gray-700 mr-1">Call ID</span>
              <a
                href="#"
                className="text-sm text-blue-600 mr-1 max-w-[180px] truncate inline-block"
                title={selectedCallId}
              >
                {selectedCallId}
              </a>
              <Button
                variant="ghost"
                className="p-0 w-6 h-6 hover:bg-gray-200 transition-colors flex items-center justify-center"
                onClick={() => setSelectedCallId("")}
              >
                <X className="h-3 w-3 text-gray-500" />
              </Button>
            </div>
          )}
          {/* Filter button */}
          <Popover open={showFilterPopover || showAgentFilterPopover || showCallIdFilterPopover} onOpenChange={(open) => {
            if (!open) {
              setShowFilterPopover(false);
              setShowAgentFilterPopover(false);
              setShowCallIdFilterPopover(false);
            }
          }}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 px-5 py-2 text-gray-500 font-semibold cursor-pointer"
                onClick={() => setShowFilterPopover(true)}
              >
                <Plus className="h-5 w-5" />
                {t("callHistoryPage.filter")}
              </Button>
            </PopoverTrigger>
            {/* filter popover content */}
            {showFilterPopover && (
              <PopoverContent className="w-64 p-0" align="start">
                <div className="py-2">
                  {filterOptions.map(opt => (
                    <Button
                      key={opt.key}
                      variant="ghost"
                      className="flex items-center w-full px-4 py-2 hover:bg-gray-50 transition-colors text-left justify-start h-auto cursor-pointer"
                      onClick={() => {
                        if (opt.key === "agent") {
                          setShowFilterPopover(false);
                          setShowAgentFilterPopover(true);
                        }
                        if (opt.key === "callId") {
                          setShowFilterPopover(false);
                          setShowCallIdFilterPopover(true);
                        }
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      <span>{opt.label}</span>
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            )}
            {showCallIdFilterPopover && (
              <PopoverContent className="w-80 p-0" align="start">
                {/* Header */}
                <div className="flex items-center gap-2 px-4 py-3 border-b">
                  <Button variant="ghost" className="cursor-pointer" size="sm"
                    onClick={() => {
                      setShowCallIdFilterPopover(false);
                      setShowFilterPopover(true);
                    }}
                  >
                    <ChevronLeft className="h-5 w-5 text-gray-500" />
                  </Button>
                  <span className="font-semibold text-base">{t("callHistoryPage.filterByCallId")}</span>
                </div>
                {/* Input */}
                <div className="px-4 py-3 border-b">
                  <input
                    type="text"
                    placeholder={t("callHistoryPage.enterCallId")}
                    className="w-full border rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={callIdInput}
                    onChange={e => setCallIdInput(e.target.value)}
                  />
                </div>
                {/* Footer */}
                <div className="flex justify-end gap-2 border-t px-4 py-3 bg-white">
                  <Button variant="ghost" className="cursor-pointer" size="sm" onClick={() => {
                    setCallIdInput("");
                    setShowCallIdFilterPopover(false);
                  }}>{t("common.cancel")}</Button>
                  <Button variant="default" className="cursor-pointer" size="sm" onClick={() => {
                    setSelectedCallId(callIdInput);
                    setCallIdInput("");
                    setShowCallIdFilterPopover(false);
                  }}>{t("common.save")}</Button>
                </div>
              </PopoverContent>
            )}
            {/* agent filter popover content */}
            {showAgentFilterPopover && (
              <PopoverContent className="w-96 p-0" align="start">
                {/* Header */}
                <div className="flex items-center gap-2 px-4 py-3 border-b">
                  <Button variant="ghost" className="cursor-pointer" size="sm"
                    onClick={() => {
                      setShowAgentFilterPopover(false);
                      setShowFilterPopover(true);
                      setAgentSearch("");
                      setPendingAgents(selectedAgents);
                    }}
                  >
                    <ChevronLeft className="h-5 w-5 text-gray-500" />
                  </Button>
                  <span className="font-semibold text-base">{t("callHistoryPage.filterByAgent")}</span>
                </div>
                {/* Search */}
                <div className="px-4 py-3 border-b">
                  <input
                    type="text"
                    placeholder={t("callHistoryPage.searchAgent")}
                    className="w-full border rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={agentSearch}
                    onChange={e => setAgentSearch(e.target.value)}
                  />
                </div>
                {/* Agent list */}
                <div className="max-h-60 overflow-y-auto px-2 py-2">
                  {sortedAgents.length === 0 && (
                    <div className="text-gray-400 text-center py-6 text-sm">{t("callHistoryPage.noAgentFound")}</div>
                  )}
                  {sortedAgents.map(agent => (
                    <label key={agent.agentId} className="flex items-start gap-2 px-2 py-2 hover:bg-gray-50 rounded cursor-pointer">
                      <input
                        type="checkbox"
                        className="mt-1"
                        checked={pendingAgents.includes(agent.agentId)}
                        onChange={() => {
                          setPendingAgents(prev =>
                            prev.includes(agent.agentId)
                              ? prev.filter(id => id !== agent.agentId)
                              : [...prev, agent.agentId]
                          );
                        }}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium text-sm">{agent.agentName}</span>
                        <span className="text-xs text-gray-500">{agent.agentId}</span>
                      </div>
                      <div className="ml-auto flex items-center">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </label>
                  ))}
                </div>
                {/* Footer */}
                <div className="flex justify-end gap-2 border-t px-4 py-3 bg-white">
                  <Button variant="ghost" className="cursor-pointer" size="sm" onClick={() => {
                    setPendingAgents(selectedAgents);
                    setShowAgentFilterPopover(false);
                    setAgentSearch("");
                  }}>{t("common.cancel")}</Button>
                  <Button variant="default" className="cursor-pointer" size="sm" onClick={() => {
                    setSelectedAgents(pendingAgents);
                    setShowAgentFilterPopover(false);
                    setAgentSearch("");
                  }}>{t("common.save")}</Button>
                </div>
              </PopoverContent>
            )}
          </Popover>
          {/* customize field button */}
          <Popover open={showCustomizePopover} onOpenChange={setShowCustomizePopover}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 px-5 py-2 text-gray-500 font-semibold cursor-pointer"
                onClick={handleCustomizeOpen}
              >
                <Settings className="h-5 w-5" />
                {t("callHistoryPage.customizeField")}
              </Button>
            </PopoverTrigger>
            {/* customize popover content */}
            <PopoverContent className="w-fit min-w-[280px] p-0" align="start">
              <div className="p-2 max-h-[70vh] overflow-y-auto">
                {allColumns.map(col => (
                  <label key={col.key} className="flex items-center gap-2 py-1 px-2 cursor-pointer">
                    <Checkbox
                      checked={pendingColumns.includes(col.key)}
                      onCheckedChange={() => handleColumnToggle(col.key)}
                      className="data-[state=checked]:bg-blue-600 data-[state=checked]:text-white data-[state=checked]:border-blue-600"
                    />
                    <span>{col.label}</span>
                  </label>
                ))}
              </div>
              <div className="flex justify-end gap-2 border-t p-2">
                <Button variant="ghost" className="cursor-pointer" size="sm" onClick={handleCustomizeCancel}>{t("common.cancel")}</Button>
                <Button variant="default" className="cursor-pointer" size="sm" onClick={handleCustomizeSave}>{t("common.save")}</Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        <div className="flex gap-4">
          {/* export button */}
          <Button variant="outline" className="flex items-center gap-2 px-4 py-2 text-gray-500 font-semibold cursor-pointer">
            <ArrowUpFromLine className="h-5 w-5" />
            {t("callHistoryPage.export")}
          </Button>
          {/* history button */}
          <Button variant="outline" size="icon" className="px-4 py-2 text-gray-500 cursor-pointer">
            <History className="h-5 w-5" />
          </Button>
        </div>
      </div>
      {/* table */}
      <div className="">
        <div className="overflow-y-auto rounded bg-white justify-center h-[70vh]">
          <Table className="border-x-0 border-t-0 border-b-0">
            {/* table header */}
            <TableHeader className="sticky top-0 z-20">
              <TableRow className="h-15 bg-gray-50">
                {allColumns.filter(col => visibleColumns.includes(col.key)).map(col => (
                  <TableHead key={col.key} className="px-10">{col.label}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            {/* table body */}
            <TableBody>
              {currentData.map((row, idx) => (
                <TableRow
                  key={idx}
                  className="h-15 py-6 group cursor-pointer"
                  onClick={() => setSelectedColumnId(row.sessionId as string)}
                >
                  {allColumns.filter(col => visibleColumns.includes(col.key)).map(col => (
                    <TableCell key={col.key} className="px-10">
                      {renderCellContent(col, row)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* pagination */}
          <div className="flex justify-center items-center mt-4 mb-2">
            <Button 
              variant="outline" 
              size="icon" 
              className="mx-1 cursor-pointer" 
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              <span className="sr-only">{t("common.previous")}</span>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <span className="mx-2 text-gray-500 font-semibold w-16 text-center">
              {currentPage} / {totalPages}
            </span>
            <Button 
              variant="outline" 
              size="icon" 
              className="mx-1 cursor-pointer"
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
            >
              <span className="sr-only">{t("common.next")}</span>
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
      </div>
      {/* Sheet for selected column */}
      <Sheet open={!!selectedColumnId} onOpenChange={open => setSelectedColumnId(open ? selectedColumnId : null)}>
        
        <SheetContent
          side="right"
          className="!min-w-0 !max-w-none overflow-y-auto"
          style={{ width: '35vw', minWidth: 0, maxWidth: 'none', maxHeight: '100vh' }}
        >
          <style>{`
            [data-slot="sheet-overlay"] { display: none !important; }
            [data-slot="sheet-content"] [data-slot="sheet-close"] {
              box-shadow: none !important;
              border: none !important;
              outline: none !important;
            }
            [data-slot="sheet-content"] [data-slot="sheet-close"]:focus {
              box-shadow: none !important;
              border: none !important;
              outline: none !important;
            }
          `}</style>
          {/* Hidden title for accessibility */}
          <SheetTitle className="sr-only">Call Details</SheetTitle>
          <div className="h-5" />
          {/* Divider */}
          <div className="w-full border-b border-gray-200 mt-4" />
          {selectedColumnId && (() => {
            const selectedRow = data.find(row => row.sessionId === selectedColumnId);
            if (!selectedRow) return null;
            
            return (
              <>
                {/* First section */}
                <div className="px-3">
                  <div className="flex items-center gap-4 mb-2">
                    <span className="text-lg font-medium">{selectedRow.time}</span>
                    <span className="text-lg py-1 rounded font-medium">
                      {selectedRow.channelType}
                    </span>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-3 mb-1 text-xs font-medium text-gray-600">
                    <span>{t("callHistoryPage.agent")}:</span>
                    {/* agent name */}
                    <span>{selectedRow.agentName}</span>
                    <span className="text-gray-400">•</span>
                    {/* agent id */}
                    <span>(
                      {selectedRow.agentId}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-5 w-5 p-0 ml-1 hover:bg-gray-100"
                        onClick={e => {
                          e.stopPropagation();
                          handleCopySessionId(selectedRow.agentId as string);
                        }}
                      >
                        {copiedSessionId === selectedRow.agentId ? (
                          <Check className="h-2 w-2 text-green-600" />
                        ) : (
                          <Copy className="h-2 w-2 text-gray-500" />
                        )}
                      </Button>
                    )</span>
                    <span className="text-gray-400">•</span>
                    {/* agent version */}
                    <span>{t("callHistoryPage.agentVersion")}: {selectedRow.agentVersion}</span>
                    <span className="text-gray-400">•</span>
                    {/* call id */}
                    <span>Call ID: <span className="font-mono">{selectedRow.sessionId}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-5 w-5 p-0 ml-1 hover:bg-gray-100"
                        onClick={e => {
                          e.stopPropagation();
                          handleCopySessionId(selectedRow.sessionId as string);
                        }}
                      >
                        {copiedSessionId === selectedRow.sessionId ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3 text-gray-500" />
                        )}
                      </Button>
                    </span>
                  </div>
                  {/* channel type */}
                  <div className="mb-1 text-xs font-medium text-gray-600">
                    <span>
                      {selectedRow.channelType === 'phone_call' ? t("callHistoryPage.phoneCall") : t("callHistoryPage.webCall")} : {selectedRow.from} <span className="mx-1">→</span> {selectedRow.to}
                    </span>
                  </div>
                  {/* duration */}
                  <div className="mb-1 text-xs font-medium text-gray-600">
                    {(() => {
                      
                      let startStr = selectedRow.time;
                      if (!/\d{2}:\d{2}/.test(startStr)) startStr += ' 00:00';
                      
                      const [min, sec] = (selectedRow.duration as string).split(':').map(Number);
                      
                      const startDate = new Date(
                        startStr.replace(/(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2})/, '$3-$2-$1T$4:$5:00')
                      );
                      
                      const endDate = new Date(startDate.getTime() + (min * 60 + sec) * 1000);
                      
                      const pad = (n: number) => n.toString().padStart(2, '0');
                      const formatDate = (d: Date) => `${pad(d.getDate())}/${pad(d.getMonth()+1)}/${d.getFullYear()} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
                      return (
                        <span>
                          {t("callHistoryPage.duration")}: {formatDate(startDate)} - {formatDate(endDate)}
                        </span>
                      );
                    })()}
                  </div>
                  {/* cost */}
                  <div className="mb-1 text-xs font-medium text-gray-600">
                    {t("callHistoryPage.cost")}: {selectedRow.cost}
                  </div>
                  {/* audio player and download button */}
                  <div className="flex items-center gap-4 mt-3">
                    <audio controls className="flex-1 max-w-[250px]" />
                    <Button
                      variant="outline"
                      size="icon"
                      className="w-12 h-12 cursor-pointer"
                      asChild
                    >
                      <a download>
                        <Download className="h-6 w-6 text-gray-600" />
                      </a>
                    </Button>
                  </div>
                </div>
                
                {/* Full divider */}
                <div className="w-full border-b border-gray-200" />
                
                {/* Second section */}
                <div className="px-3">
                  <div className="space-y-3">
                    <h3 className="text-sm font-semibold">{t("callHistoryPage.conversationAnalysis")}</h3>
                    <div className="text-xs text-gray-500 mb-2">{t("callHistoryPage.preset")}</div>
                    <div className="grid grid-cols-2 gap-y-2 gap-x-4 items-center">
                      {/* Call Successful */}
                      <div className="flex items-center gap-2">
                      <SquareCheck className="h-4 w-4 text-gray-500" />
                        <span className="text-xs">{t("callHistoryPage.callSuccessful")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${selectedRow.sessionOutcome === 'Successful' ? 'bg-green-600' : 'bg-red-600'}`} />
                        <span className={`text-xs font-medium`}>{selectedRow.sessionOutcome === 'Successful' ? t("callHistoryPage.successful") : t("callHistoryPage.unsuccessful")}</span>
                      </div>

                      {/* Call Status */}
                      <div className="flex items-center gap-2">
                        <Headset className="h-4 w-4 text-gray-500" />
                        <span className="text-xs">{t("callHistoryPage.callStatus")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="inline-block w-2 h-2 rounded-full bg-gray-300" />
                        <span className="text-xs font-medium">{selectedRow.sessionStatus.charAt(0).toUpperCase() + selectedRow.sessionStatus.slice(1)}</span>
                      </div>

                      {/* User Sentiment */}
                      <div className="flex items-center gap-2">
                        <MicVocal className="h-4 w-4 text-gray-500" />
                        <span className="text-xs">{t("callHistoryPage.userSentiment")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${selectedRow.userSentiment === 'Positive' ? 'bg-green-600' : selectedRow.userSentiment === 'Neutral' ? 'bg-blue-600' : 'bg-red-600'}`} />
                        <span className={`text-xs font-medium`}>{selectedRow.userSentiment}</span>
                      </div>

                      {/* Disconnection Reason */}
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-xs">{t("callHistoryPage.disconnectionReason")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${selectedRow.endSession === 'user hangup' || selectedRow.endSession === 'agent hangup' ? 'bg-green-600' : 'bg-red-600'}`} />
                        <span className={`text-xs font-medium`}>{selectedRow.endSession.replace(/_/g, ' ')}</span>
                      </div>

                      {/* End to End Latency */}
                      <div className="flex items-center gap-2">
                        <Gauge className="h-4 w-4 text-gray-500" />
                        <span className="text-xs">{t("callHistoryPage.endToEndLatency")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-gray-700">{selectedRow.endToEndLatency}</span>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Full divider */}
                <div className="w-full border-b border-gray-200" />
                
                {/* Third section */}
                <div className="p-3 pt-0">
                  <div className="space-y-3">
                    <h3 className="text-sm font-semibold">{t("callHistoryPage.summary")}</h3>
                    <div className="text-sm text-gray-800">
                      {`The user seems to be discussing their preferences or thoughts about a service, but the conversation is somewhat unclear. The agent acknowledges the user's input and thanks them, indicating a polite interaction.`}
                    </div>
                  </div>
                </div>

                <div className="w-full border-b border-gray-200" />
                
                {/* Forth section */}
                <div className="p-3 pt-0">
                  <div className="space-y-3">
                    <h3 className="text-sm font-semibold">{t("callHistoryPage.transcription")}</h3>
                    <div className="flex w-full flex-col gap-6">
                      <Tabs defaultValue="transcription">
                        <TabsList>
                          <TabsTrigger value="transcription" className="cursor-pointer">{t("callHistoryPage.transcription")}</TabsTrigger>
                          <TabsTrigger value="data" className="cursor-pointer">{t("callHistoryPage.data")}</TabsTrigger>
                          <TabsTrigger value="detail-logs" className="cursor-pointer">{t("callHistoryPage.detailLogs")}</TabsTrigger>
                        </TabsList>
                        <TabsContent value="transcription">
                          <div className="text-gray-500 text-sm">{t("callHistoryPage.noTranscription")}</div>
                        </TabsContent>
                        <TabsContent value="data">
                          {/* Dynamic Variables */}
                          <div className="mb-4">
                            <div className="font-semibold text-sm text-gray-700 mb-1">{'{ } Dynamic Variables'}</div>
                            <div className="bg-gray-50 rounded px-4 py-2 text-gray-400 text-sm">{t("callHistoryPage.noDynamicVariables")}</div>
                          </div>
                          {/* Extracted Dynamic Variables */}
                          <div className="mb-4">
                            <div className="font-semibold text-sm text-gray-700 mb-1">{'{ } Extracted Dynamic Variables'}</div>
                            <div className="bg-gray-50 rounded px-4 py-2 text-gray-400 text-sm">{t("callHistoryPage.noExtractedDynamicVariables")}</div>
                          </div>
                          {/* Metadata */}
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <span className="flex items-center font-semibold text-sm text-gray-700">
                                <Tag className="h-4 w-4 mr-1" />
                                Metadata
                              </span>
                            </div>
                            <div className="bg-gray-50 rounded px-4 py-3 text-sm">
                              <div className="mb-3">
                                <span className="block font-semibold">tenantName</span>
                                <span className="ml-0 text-gray-500 block">{selectedRow.tenantName}</span>
                              </div>
                              <div className="mb-3">
                                <span className="block font-semibold">autoAnalysis</span>
                                <span className="ml-0 text-gray-500 block">{selectedRow.autoAnalysis ? 'true' : 'false'}</span>
                              </div>
                              <div>
                                <span className="block font-semibold">realAgentId</span>
                                <span className="ml-0 text-gray-500 block">{selectedRow.realAgentId}</span>
                              </div>
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent value="detail-logs">
                          <div className="text-gray-500 text-sm">Detail Logs content</div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </div>
              </>
            );
          })()}
         
        </SheetContent>
      </Sheet>
    </div>
  );
}
