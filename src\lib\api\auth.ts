import {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '@/lib/types/auth';

const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';
const REFRESH_TOKEN_ENDPOINT = 'https://placeholder-refresh-endpoint.com/api/Auth/refresh'; // Placeholder

class AuthApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'AuthApiError';
  }
}

async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorCode = response.status.toString();

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
      errorCode = errorData.code || errorCode;
    } catch {
      // If response is not JSON, use default error message
    }

    throw new AuthApiError(errorMessage, response.status, errorCode);
  }

  return response.json();
}

export const authApi = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiRequest<any>(
        `${API_BASE_URL}/Auth/authenticate-user`,
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        }
      );

      console.log('API Response:', response); // Debug log

      // Transform the response to match our expected format
      const transformedResponse: LoginResponse = {
        user: {
          id: response.userId || response.id || 'unknown',
          email: response.email || credentials.email,
          name: response.name || response.fullName || response.userName,
          role: response.role || 'user',
          avatar: response.avatar || response.profilePicture,
        },
        accessToken: response.accessToken || response.token || response.access_token,
        refreshToken: response.refreshToken || response.refresh_token || 'placeholder-refresh-token',
        expiresIn: response.expiresIn || response.expires_in || 3600, // Default 1 hour
      };

      console.log('Transformed Response:', transformedResponse); // Debug log

      return transformedResponse;
    } catch (error) {
      console.error('Login API Error:', error); // Debug log
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during login');
    }
  },

  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    try {
      const response = await apiRequest<RefreshTokenResponse>(
        REFRESH_TOKEN_ENDPOINT,
        {
          method: 'POST',
          body: JSON.stringify(request),
        }
      );

      return response;
    } catch (error) {
      if (error instanceof AuthApiError) {
        throw error;
      }
      throw new AuthApiError('Network error occurred during token refresh');
    }
  },

  async logout(token: string): Promise<void> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/logout`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      // Logout should not fail the client-side logout process
      console.warn('Server logout failed:', error);
    }
  },

  async validateToken(token: string): Promise<boolean> {
    try {
      await apiRequest(`${API_BASE_URL}/Auth/validate`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      return false;
    }
  },
};

export { AuthApiError };
