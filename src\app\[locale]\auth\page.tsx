import Image from "next/image";
import AuthCard from "@/components/pages/auth-page/auth-card";

export default function AuthPage() {
  return (
    <div className="flex flex-col xl:flex-row h-screen w-full">
      <div className="relative xl:w-2/3 w-full h-full">
        <Image
          src="https://picsum.photos/1600/1200"
          alt="Random Visual"
          fill
          className="object-cover"
          priority
        />

        <div
          className="xl:hidden absolute top-1/2 left-1/2 transform -translate-1/2 w-full max-w-md rounded-t-lg bg-white bg-opacity-90 shadow-lg"
          style={{ backdropFilter: "blur(10px)" }}
        >
          <AuthCard />
        </div>
      </div>

      <div className="hidden xl:flex xl:w-1/3 bg-white items-center justify-center p-8">
        <AuthCard />
      </div>
    </div>
  );
}
