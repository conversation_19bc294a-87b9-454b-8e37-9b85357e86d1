import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n";
import { NextRequest, NextResponse } from "next/server";

const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/agents',
  '/analytics',
  '/api-key',
  '/billing',
  '/call-history',
  '/knowledge-base',
  '/phone-numbers',
  '/webhooks',
  '/batch-call',
];

const publicRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/',
];

// they're our redirect paths, if user is already authenticated
const authRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
];

function getPathnameWithoutLocale(pathname: string): string {
  const segments = pathname.split('/');
  if (segments.length > 1 && segments[1].length === 2) {
    return '/' + segments.slice(2).join('/');
  }
  return pathname;
}

function isProtectedRoute(pathname: string): boolean {
  const cleanPath = getPathnameWithoutLocale(pathname);
  return protectedRoutes.some(route =>
    cleanPath.startsWith(route) || cleanPath.includes(route)
  );
}

function isAuthRoute(pathname: string): boolean {
  const cleanPath = getPathnameWithoutLocale(pathname);
  return authRoutes.some(route =>
    cleanPath.startsWith(route) || cleanPath.includes(route)
  );
}

function isPublicRoute(pathname: string): boolean {
  const cleanPath = getPathnameWithoutLocale(pathname);
  return publicRoutes.some(route =>
    cleanPath === route || cleanPath.startsWith(route)
  );
}

function getTokenFromRequest(request: NextRequest): string | null {
  const tokenFromCookie = request.cookies.get('voice-ai-auth')?.value;

  if (tokenFromCookie) {
    try {
      const authData = JSON.parse(tokenFromCookie);
      return authData?.state?.tokens?.accessToken || null;
    } catch {
      return null;
    }
  }

  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true; 
  }
}

async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = getTokenFromRequest(request);
  const isAuthenticated = token && !isTokenExpired(token);

  const segments = pathname.split('/');
  const locale = segments.length > 1 && segments[1].length === 2 ? segments[1] : 'en';

  if (isAuthenticated && isAuthRoute(pathname)) {
    return NextResponse.redirect(new URL(`/${locale}/dashboard`, request.url));
  }

  if (!isAuthenticated && isProtectedRoute(pathname)) {
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

const intlMiddleware = createMiddleware(routing);

export default async function middleware(request: NextRequest) {
  const authResponse = await authMiddleware(request);

  if (authResponse.status === 302) {
    return authResponse;
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: "/((?!api|trpc|_next|_vercel|.*\\..*).*)",
};
