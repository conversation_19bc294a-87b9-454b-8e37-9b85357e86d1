import AgentConf from "@/components/pages/agent-page/agent-conf";
import Agent<PERSON><PERSON> from "@/components/pages/agent-page/agent-main";
import AgentTest from "@/components/pages/agent-page/agent-test";
import { Card, CardContent } from "@/components/ui/card";

export default async function AgentPage({
  params,
}: {
  params: Promise<{ agentId: number }>
}) {
  const { agentId } = await params
   return (
    <div className="relative flex gap-2 p-4 max-h-screen h-full">
      <Card className="flex-2">
        <CardContent>
          <AgentMain agentId={agentId} />
        </CardContent>
      </Card>

      <Card className="flex-1  max-2xl:hidden">
        <CardContent>
          <AgentConf />
        </CardContent>
      </Card>

      <Card className="flex-1 max-2xl:hidden">
        <CardContent className="h-full">
          <AgentTest />
        </CardContent>
      </Card>
    </div>
  );
}
