// components/SidebarWrapper.tsx
"use client";

import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset } from "@/components/ui/sidebar";
import { usePathname } from "@/i18n";
import { routes } from "@/lib/constants/routes";

export function SidebarWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isAuth = pathname.startsWith(routes.auth);

  if (isAuth) {
    return <>{children}</>;
  }

  return (
    <>
      <AppSidebar />
      <SidebarInset>{children}</SidebarInset>
    </>
  );
}
