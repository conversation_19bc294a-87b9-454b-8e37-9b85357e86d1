import { create } from "zustand";

export type AgentConfigWhoFirst = "agent" | "customer";

type AgentConfigStructure = {
  llm: {
    provider: string;
    model: string;
    baseurl?: string | undefined;
  };
  tts: {
    provider: string;
    model: string;
    voiceid: string;
    baseurl?: string | undefined;
  };
  stt: {
    provider: string;
    model: string;
    language: string;
    baseurl?: string | undefined;
  };
  firstMessage: {
    whoFirst: AgentConfigWhoFirst;
    type?: string | undefined;
    text?: string | undefined;
    allowinterruptions?: string | undefined;
  };
  prompt: string;
};

type AgentConfigState = {
  config: AgentConfigStructure;

  setConfig: (config: AgentConfigStructure) => void;
};

export const useAgentConfigStore = create<AgentConfigState>((set) => ({
  config: {
    llm: {
      provider: "openai",
      model: "gpt-4.1",
    },
    tts: {
      provider: "openai",
      model: "gpt-4o-mini-tts",
      voiceid: "alloy",
    },
    stt: {
      provider: "openai",
      model: "gpt-4o-transcribe",
      language: "tr"
    },
    firstMessage: {
      whoFirst: "customer"
    },
    prompt: "",
  },

  setConfig: (config) => set((state) => ({ config }))
}));
