import { Info, <PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type AgentConfTooltipProps = { type?: "slow" | "info"; text: string };

const tooltipPropsByType = {
  slow: { icon: Turtle },
  info: { icon: Info },
};

const AgentConfTooltip = ({ type = "slow", text }: AgentConfTooltipProps) => {
  const tooltipProps = tooltipPropsByType[type];

  return (
    <Tooltip>
      <TooltipTrigger>
        <Badge variant="outline">
          <tooltipProps.icon className="text-gray-400" size={12} />
        </Badge>
      </TooltipTrigger>
      <TooltipContent className="max-w-2xs">
        <p>{text}</p>
      </TooltipContent>
    </Tooltip>
  );
};

export default AgentConfTooltip;
