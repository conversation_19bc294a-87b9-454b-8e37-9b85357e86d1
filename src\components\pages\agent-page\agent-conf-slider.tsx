"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";
import { Slider } from "@/components/ui/slider";

type AgentConfSliderProps = {
  max?: number;
  min?: number;
  defaultValue: number;
  step?: number;
  hasTimeUnit?: boolean;
};

const AgentConfSlider = ({
  defaultValue,
  min,
  max,
  step = 0.01,
  hasTimeUnit,
}: AgentConfSliderProps) => {
  const [value, setValue] = useState(defaultValue);
  const t = useTranslations();

  let valueDivider = 1;
  let shownUnit = t("AgentConf.common.shortSecond");
  if (hasTimeUnit) {
    if (value > 60) {
      valueDivider = 60;
      shownUnit = t("AgentConf.common.shortMinute");
    }
    if (value > 3600) {
      valueDivider = 3600;
      shownUnit = t("AgentConf.common.shortHour");
    }
  }

  const shownValue = (value / valueDivider).toFixed(2);

  return (
    <div className="flex pl-1 gap-6 items-center">
      <Slider
        min={min}
        value={[value]}
        max={max}
        step={step}
        defaultValue={[defaultValue]}
        onValueChange={([newValue]) => {
          setValue(newValue);
        }}
      />
      {value && (
        <p className="w-16 font-semibold text-center">
          {shownValue} {hasTimeUnit && shownUnit}
        </p>
      )}
    </div>
  );
};

export default AgentConfSlider;
