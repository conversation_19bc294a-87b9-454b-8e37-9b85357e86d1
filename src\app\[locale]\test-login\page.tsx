'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { authApi } from '@/lib/api/auth';
import { useAuthStore } from '@/store/auth-store';

export default function TestLoginPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const { user, tokens, isAuthenticated } = useAuthStore();

  const testDirectAPI = async () => {
    setLoading(true);
    try {
      const response = await authApi.login({ email, password });
      setResult({ success: true, data: response });
    } catch (error) {
      setResult({ success: false, error: error.message });
    }
    setLoading(false);
  };

  const testStoreLogin = async () => {
    setLoading(true);
    try {
      await useAuthStore.getState().login({ email, password });
      setResult({ success: true, message: 'Store login successful' });
    } catch (error) {
      setResult({ success: false, error: error.message });
    }
    setLoading(false);
  };

  const clearStorage = () => {
    localStorage.removeItem('voice-ai-auth');
    useAuthStore.getState().logout();
    setResult(null);
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Auth System Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label>Email:</label>
                <Input
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email"
                />
              </div>
              <div>
                <label>Password:</label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={testDirectAPI} disabled={loading}>
                Test Direct API
              </Button>
              <Button onClick={testStoreLogin} disabled={loading}>
                Test Store Login
              </Button>
              <Button onClick={clearStorage} variant="outline">
                Clear Storage
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Current Auth State</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}</p>
              <p><strong>Tokens:</strong> {tokens ? 'Present' : 'None'}</p>
              {tokens && (
                <div className="text-xs">
                  <p><strong>Access Token:</strong> {tokens.accessToken?.substring(0, 50)}...</p>
                  <p><strong>Expires At:</strong> {new Date(tokens.expiresAt).toLocaleString()}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>LocalStorage Content</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-2 rounded">
              {localStorage.getItem('voice-ai-auth') || 'Empty'}
            </pre>
          </CardContent>
        </Card>

        {result && (
          <Card>
            <CardHeader>
              <CardTitle>Test Result</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs bg-gray-100 p-2 rounded">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
