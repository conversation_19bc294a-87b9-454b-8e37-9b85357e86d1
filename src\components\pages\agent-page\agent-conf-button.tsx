import { Plus, Settings } from "lucide-react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";

type AgentConfButtonProps = { type?: "add" | "set" };

const AgentConfButton = ({ type = "add" }: AgentConfButtonProps) => {
  const t = useTranslations();

  const buttonPropsByType = {
    add: { icon: Plus, text: t("AgentConf.common.add") },
    set: { icon: Settings, text: t("AgentConf.common.setUp") },
  };

  const buttonProps = buttonPropsByType[type];

  return (
    <Button size="sm">
      <buttonProps.icon /> {buttonProps.text}
    </Button>
  );
};

export default AgentConfButton;
