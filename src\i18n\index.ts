import { createNavigation } from "next-intl/navigation";
import { defineRouting } from "next-intl/routing";

import { routes } from "@/lib/constants/routes";
import type en from "./messages/en";
export { default as tr } from "./messages/tr";
export { default as en } from "./messages/en";

export type LocaleType = "tr" | "en";
export const defaultLocale: LocaleType = "tr";

export const routing = defineRouting({
  locales: ["en", "tr"],
  localeDetection: false,
  defaultLocale: defaultLocale,
  localePrefix: "as-needed",
  pathnames: {
    [routes.agents]: routes.agents,
    [routes.agent]: routes.agent,
    [routes.home]: routes.home,
    [routes.knowledgeBase]: routes.knowledgeBase,
    [routes.phoneNumbers]: routes.phoneNumbers,
    [routes.batchCall]: routes.batchCall,
    [routes.callHistory]: routes.callHistory,
    [routes.analytics]: routes.analytics,
    [routes.billing]: routes.billing,
    [routes.apiKey]: routes.apiKey,
    [routes.webhooks]: routes.webhooks,
    [routes.auth]: routes.auth,
  },
});

export type Locales = typeof routing.locales;
export type LocalizationKeys = typeof en;

export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
