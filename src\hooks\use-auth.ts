import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/store/auth-store';
import { authApi, AuthApiError } from '@/lib/api/auth';
import { LoginRequest } from '@/lib/types/auth';
import { useRouter, useParams } from 'next/navigation';
import { useCallback, useEffect } from 'react';

export const AUTH_QUERY_KEYS = {
  auth: ['auth'] as const,
  user: ['auth', 'user'] as const,
  validate: ['auth', 'validate'] as const,
} as const;

export function useAuth() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const locale = params?.locale || 'en';
  
  const {
    user,
    tokens,
    isAuthenticated,
    isLoading: storeLoading,
    error,
    login: storeLogin,
    logout: storeLogout,
    refreshTokens,
    setError,
    clearError,
    initialize,
  } = useAuthStore();

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      await storeLogin(credentials);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.auth });
      router.push(`/${locale}/dashboard`); // Redirect after successful login
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      if (tokens?.accessToken) {
        await authApi.logout(tokens.accessToken);
      }
      storeLogout();
    },
    onSuccess: () => {
      queryClient.clear(); // Clear all cached data
      router.push(`/${locale}/login`);
    },
  });

  // Refresh token mutation
  const refreshMutation = useMutation({
    mutationFn: refreshTokens,
    onError: () => {
      // If refresh fails, logout user
      logoutMutation.mutate();
    },
  });

  // Token validation query
  const { data: isTokenValid } = useQuery({
    queryKey: [...AUTH_QUERY_KEYS.validate, tokens?.accessToken],
    queryFn: async () => {
      if (!tokens?.accessToken) return false;
      return authApi.validateToken(tokens.accessToken);
    },
    enabled: !!tokens?.accessToken && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
    retry: false,
  });

  // Auto-refresh token when it's about to expire
  useEffect(() => {
    if (!tokens || !isAuthenticated) return;

    const timeUntilExpiry = tokens.expiresAt - Date.now();
    const refreshThreshold = 5 * 60 * 1000; // 5 minutes before expiry

    if (timeUntilExpiry <= refreshThreshold && timeUntilExpiry > 0) {
      refreshMutation.mutate();
    }

    // Set up timer to refresh token
    const timer = setTimeout(() => {
      if (isAuthenticated && tokens) {
        refreshMutation.mutate();
      }
    }, Math.max(timeUntilExpiry - refreshThreshold, 0));

    return () => clearTimeout(timer);
  }, [tokens, isAuthenticated, refreshMutation]);

  // Initialize auth on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Logout if token validation fails
  useEffect(() => {
    if (isTokenValid === false && isAuthenticated) {
      logoutMutation.mutate();
    }
  }, [isTokenValid, isAuthenticated, logoutMutation]);

  const login = useCallback(
    (credentials: LoginRequest) => {
      clearError();
      return loginMutation.mutateAsync(credentials);
    },
    [loginMutation, clearError]
  );

  const logout = useCallback(() => {
    clearError();
    logoutMutation.mutate();
  }, [logoutMutation, clearError]);

  const isLoading = storeLoading || loginMutation.isPending || logoutMutation.isPending;

  return {
    // State
    user,
    tokens,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login,
    logout,
    clearError,
    
    // Mutation states
    isLoginPending: loginMutation.isPending,
    isLogoutPending: logoutMutation.isPending,
    isRefreshPending: refreshMutation.isPending,
    
    // Token validation
    isTokenValid,
  };
}
